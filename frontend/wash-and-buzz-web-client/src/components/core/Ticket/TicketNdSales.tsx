'use client'
import {
  getPaymentData,
  updateAmounts,
  updatePayloadData,
} from '@/components/core/Ticket/Calculations'
import CurrentTicket from '@/components/core/Ticket/CurrentTicket'
import PaymentModalWrapper from '@/components/core/Ticket/PaymentModalWrapper'
import { QuickCheckout } from '@/components/core/Ticket/QuickCheckout'
import Sales from '@/components/core/Ticket/Sales'
import VariantBox from '@/components/core/Ticket/VariantBox'
import { showErrorToast } from '@/components/core/Toast/CustomToast'
import useGlobalScrollEvent from '@/hooks/useGlobalScrollEvent'
import { useItemDataMap } from '@/hooks/useItemDataMap'
import { useUpdateCustomerTickets } from '@/hooks/useUpdateCustomerTickets'
import {
  setCashDrawerPermission,
  setIsPrintButtonExist,
  setTicketTotalAmount,
} from '@/store/actions/common.action'
import {
  setAssignedVariantData,
  setCategoryPosData,
  setIsButtonDisabled,
  setIsItemSelected,
  setIsPayOnly,
  setOpenCardManualPaymentModal,
  setOpenCardPaymentModal,
  setOpenCashPaymentModal,
  setOpenCheckoutTicketModal,
  setOpenCheckPaymentOptionModal,
  setOpenOtherPaymentOptionModal,
  setOpenPayLaterModal,
  setOpenPaymentCustomAmountModal,
  setOpenPaymentPrintModal,
  setOpenTicketCreatedModal,
  setQuantityModalOpen,
  setRequiredVariants,
  setSelectedItem,
  setSelectedItemIndex,
  setTotalLineItem,
  setVariantModalOpen,
} from '@/store/actions/currentTicketState.action'
import {
  addOrEditCurrentTicketData,
  getTicketsForCheckout,
  removeQuickCheckoutTicketsData,
  resetCheckedTicketData,
  setLinItemState,
  ticketPrintSucceededAction,
  updateTicketPaymentData,
} from '@/store/actions/ticket.action'
import { ItemPrices } from '@/types/module/itemModule'
import { PaymentIncentivesType } from '@/types/module/priceIncentiveModule'
import { PosLayout } from '@/types/module/storeModule'
import {
  PaymentStatusForFilter,
  TicketStatusForFilter,
} from '@/types/module/ticketFiltersModule'
import {
  EDIT_TICKET_ACTION_TYPE,
  LineItemState,
  PaidAmountData,
  PaymentDetail,
  PaymentMethodType,
  PaymentTypes,
  PosLayoutMode,
  SublineType,
  TicketData,
  TicketDataBE,
  TicketNdSalesProps,
} from '@/types/module/ticketNdSalesModule'
import {
  SelectedVariantData,
  VariantItemData,
} from '@/types/module/variantsModule'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { TicketsData } from '@/types/store/reducers/tickets.reducer'
import {
  checkRequiredVariantsNumber,
  updateAllAssignedVariants,
} from '@/utils/arrays'
import {
  PAST_SIXTY_DAYS_DATE,
  TICKET,
  TICKET_DATE_FORMAT,
} from '@/utils/constant'
import { convertToTimeZone } from '@/utils/date'
import {
  calculatePayableAmount,
  findItemIndexBySelectedIndex,
  getPaymentIncentiveData,
  isButtonEnabled,
  roundAmountByTwoDigits,
} from '@/utils/functions'
import { appRoutes } from '@/utils/routes'
import { translation } from '@/utils/translation'
import { format } from 'date-fns'
import Cookies from 'js-cookie'
import { usePathname } from 'next/navigation'
import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Box } from 'theme-ui'

const TicketNdSales: FC<TicketNdSalesProps> = () => {
  const dispatch = useDispatch()
  const updateCustomerTickets = useUpdateCustomerTickets()
  const pathName = usePathname()
  const isProgrammaticScroll = useRef(false)
  const saleRef = useRef<HTMLDivElement>(null)
  const variantRef = useRef<HTMLDivElement>(null)
  const quickCheckoutCalledRef = useRef(false)

  const currentTicketData = useSelector(
    (state: MainStoreType) => state.ticketsData
  )

  const customerData = useSelector(
    (state: MainStoreType) => state.customerData.selectedCustomer
  )

  const itemsData = useSelector((state: MainStoreType) => state.itemData)
  const storeValue = useSelector((state: MainStoreType) => state.storeData)
  const categoryData = useSelector((state: MainStoreType) => state.categoryData)
  const selectedCustomerData = useSelector(
    (state: MainStoreType) => state.customerData.selectedCustomer
  )
  const variantData = useSelector((state: MainStoreType) => state.variantsData)
  const currentTicketState = useSelector(
    (state: MainStoreType) => state.currentTicketState
  )
  const customersList = useSelector(
    (state: MainStoreType) => state.customerData
  )
  const discountType = useSelector((state: MainStoreType) => {
    if (Array.isArray(state.taxSaleData.data)) {
      const taxSaleData = state.taxSaleData.data
      return taxSaleData.find((taxSale) => taxSale.isSelected)?.name ==
        'NET_SALES'
        ? 'PRE_TAX'
        : 'POST_TAX'
    }
    return 'PRE_TAX'
  })

  const currentPaymentIncentive = useSelector((state: MainStoreType) => {
    const paymentIncentives = Array.isArray(state.priceIncentiveData.data)
      ? state.priceIncentiveData.data
      : []
    return paymentIncentives.find(
      (paymentIncentive) => paymentIncentive.isSelected
    )
  })

  const ticketsData: TicketsData = useSelector(
    (state: MainStoreType) => state.ticketsData
  )

  const [isCheckout, setIsCheckout] = useState<boolean>(false)
  const [isShowMore, setIsShowMore] = useState<boolean>(false)
  const [payableAmount, setPayableAmount] = useState<number>()
  const [paymentType, setPaymentType] = useState<PaymentMethodType>()
  const [paidValue, setPaidValue] = useState<number>()
  const [isCheck, setIsCheck] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState(false)

  const totalAmount = useMemo(() => {
    const selectedTicketData = ticketsData?.quickCheckoutTicket?.filter(
      (value) => value.isSelected
    )
    return (
      selectedTicketData?.reduce(
        (totalAmount, ticket) => totalAmount + (ticket?.balance || 0),
        0
      ) || 0
    )
  }, [ticketsData?.quickCheckoutTicket])

  const isNextButtonDisabled = useMemo(() => {
    return ticketsData?.quickCheckoutTicket?.every((value) => !value.isSelected)
  }, [ticketsData?.quickCheckoutTicket])

  const getQuickCheckoutData = useCallback(
    (fetchMoreData?: boolean) => {
      if (quickCheckoutCalledRef.current) {
        return
      }
      quickCheckoutCalledRef.current = true
      setIsLoading(true)
      const continuationToken = Cookies.get(translation.CONTINUATION_TOKEN)
      const decode = decodeURIComponent(JSON.stringify(continuationToken))
      const selectedStartDate = convertToTimeZone(
        PAST_SIXTY_DAYS_DATE,
        storeValue.data?.timeZone as string
      )

      const selectedEndDate = convertToTimeZone(
        new Date(),
        storeValue.data?.timeZone as string
      )

      dispatch(
        getTicketsForCheckout(
          format(selectedStartDate, TICKET_DATE_FORMAT),
          format(selectedEndDate, TICKET_DATE_FORMAT),
          customersList?.selectedCustomer?.storeCustomerId as number,
          translation.QUICK_CHECKOUT_FILTER,
          () => {
            setIsLoading(false)
          },
          fetchMoreData || false,
          fetchMoreData ? decode : ''
        )
      )
    },
    [storeValue, customersList, dispatch]
  )

  useEffect(() => {
    if (
      storeValue &&
      storeValue.data &&
      pathName === appRoutes.dashboard &&
      !quickCheckoutCalledRef.current
    ) {
      getQuickCheckoutData()
    }
  }, [pathName, getQuickCheckoutData, storeValue])

  const actualTicketData: TicketData | undefined = useMemo(() => {
    if (currentTicketData?.data && currentTicketData.data.length > 0) {
      return currentTicketData.data.find(
        (ticket) =>
          ticket.number === currentTicketData.currentTicket?.currentTicketId
      )
    }
    return
  }, [currentTicketData.currentTicket?.currentTicketId, currentTicketData.data])

  const executeIfEditable = useCallback(
    (callback: () => void, isPriceAdjustment?: boolean) => {
      if (
        currentTicketData?.currentTicket?.isEdit
          ? actualTicketData?.ticketStatus === TicketStatusForFilter.OPEN ||
            (isPriceAdjustment &&
              actualTicketData?.ticketStatus === TicketStatusForFilter.CLOSED &&
              (actualTicketData?.paymentStatus ===
                PaymentStatusForFilter.UNPAID ||
                actualTicketData?.paymentStatus ===
                  PaymentStatusForFilter.PARTIAL))
          : true
      ) {
        callback()
      }
    },
    [currentTicketData.currentTicket?.isEdit, actualTicketData]
  )

  const handlePaidAmount = async (
    paidAmount: PaidAmountData | null,
    isPayOnly: boolean
  ) => {
    setPaymentType(paidAmount?.type)
    if (totalAmount !== undefined) {
      setIsLoading(true)

      const ticketIds = ticketsData?.quickCheckoutTicket
        ?.filter(
          (ticket) =>
            ticket?.ticketStatus === TicketStatusForFilter.OPEN &&
            ticket.isSelected
        )
        ?.map((ticket) => ticket.number) as number[]
      let firstResponse: { success: boolean; errorMessage?: string } = {
        success: false,
      }
      if (ticketIds && ticketIds?.length > 0 && !isPayOnly) {
        const getPayloadData = updatePayloadData(
          storeValue.data?.id as number,
          ticketIds,
          PaymentTypes.CHECKOUT
        )
        firstResponse = await new Promise<{
          success: boolean
          errorMessage?: string
        }>((resolve) => {
          dispatch(
            updateTicketPaymentData(
              getPayloadData,
              (
                res: boolean,
                ticketData: TicketDataBE[] | null,
                errorMessage?: string
              ) => {
                if (res) {
                  updateCustomerTickets(ticketData)
                  if (currentTicketState?.openCheckoutTicketModal) {
                    dispatch(setOpenCheckoutTicketModal(false))
                    dispatch(setOpenPaymentPrintModal(true))
                  }
                  resolve({ success: true })
                } else {
                  resolve({ success: false, errorMessage })
                }
              }
            )
          )
        })
      } else {
        firstResponse.success = true
      }
      if (paidAmount === null) {
        firstResponse.success = false
        dispatch(resetCheckedTicketData())
        setIsLoading(false)
      }

      if (firstResponse.success) {
        setPaymentType(paidAmount?.type)
        if (totalAmount !== undefined && totalAmount !== 0) {
          const totalAmountsNeedsToPay =
            (currentPaymentIncentive?.name ===
              PaymentIncentivesType.CASH_DISCOUNT &&
              paidAmount?.type === PaymentMethodType.CASH) ||
            (currentPaymentIncentive?.name ===
              PaymentIncentivesType.CREDIT_CARD_SURCHARGE &&
              paidAmount?.type === PaymentMethodType.CREDIT_CARD)
              ? (payableAmount as number)
              : totalAmount

          let paymentData
          if (
            paidAmount?.type &&
            paidAmount?.type !== PaymentMethodType.PAY_LATER
          ) {
            let paymentIncentive
            if (totalAmountsNeedsToPay > 0) {
              paymentIncentive = getPaymentIncentiveData(
                paidAmount?.type as PaymentMethodType,
                Math.min(totalAmountsNeedsToPay, paidAmount?.amount),
                currentPaymentIncentive,
                paidAmount?.type == PaymentMethodType.CASH &&
                  paidAmount?.amount >= totalAmountsNeedsToPay
                  ? totalAmount
                  : undefined
              )
            }
            const changeAmount = roundAmountByTwoDigits(
              paidAmount &&
                paidAmount.type === PaymentMethodType.CASH &&
                paidAmount.amount > totalAmountsNeedsToPay
                ? paidAmount.amount -
                    totalAmount -
                    (paymentIncentive?.amount || 0)
                : 0
            )
            paymentData = getPaymentData(
              paidAmount?.type,
              changeAmount,
              0,
              paidAmount as PaidAmountData,
              paymentIncentive
            )
          }
          if (paymentData) {
            const ticketIdWithPayment = ticketsData?.quickCheckoutTicket
              ?.filter((ticket) => ticket?.balance !== 0 && ticket.isSelected)
              ?.map((ticket) => ticket.number as number)
            const getPaymentDetails = updatePayloadData(
              storeValue.data?.id as number,
              ticketIdWithPayment as number[],
              PaymentTypes.PAYMENT,
              paymentData as PaymentDetail
            )
            dispatch(
              updateTicketPaymentData(
                getPaymentDetails,
                (
                  res: boolean,
                  ticketData: TicketDataBE[] | null,
                  errorMessage?: string
                ) => {
                  if (res) {
                    if (
                      paidAmount &&
                      paidAmount.type === PaymentMethodType.PAY_LATER
                    ) {
                      dispatch(setOpenPayLaterModal(false))
                      dispatch(setOpenTicketCreatedModal(true))
                    }
                    if (
                      paidAmount &&
                      paidAmount.type === PaymentMethodType.CASH
                    ) {
                      dispatch(setOpenPaymentCustomAmountModal(false))
                      dispatch(setOpenCashPaymentModal(false))
                      dispatch(setCashDrawerPermission(true))
                    }
                    if (
                      paidAmount &&
                      paidAmount.type === PaymentMethodType.CHECK
                    ) {
                      dispatch(setOpenCheckPaymentOptionModal(false))
                      setIsCheck(true)
                      dispatch(setCashDrawerPermission(true))
                    }
                    if (
                      paidAmount &&
                      paidAmount.type === PaymentMethodType.CREDIT_CARD
                    ) {
                      dispatch(setOpenCardPaymentModal(false))
                      dispatch(setOpenCardManualPaymentModal(false))
                    }
                    if (
                      paidAmount &&
                      paidAmount.type === PaymentMethodType.OTHER
                    ) {
                      dispatch(setOpenOtherPaymentOptionModal(false))
                      setIsCheck(true)
                    }
                    dispatch(resetCheckedTicketData())
                    dispatch(setTicketTotalAmount(totalAmount))
                    setPaidValue(paidAmount?.amount)
                    dispatch(setIsPrintButtonExist(true))
                    dispatch(setOpenPaymentPrintModal(true))
                    quickCheckoutCalledRef.current = false
                    dispatch(removeQuickCheckoutTicketsData())
                    getQuickCheckoutData()
                    if (ticketData) {
                      dispatch(ticketPrintSucceededAction(ticketData))
                    }
                  } else {
                    errorMessage && showErrorToast(errorMessage)
                  }
                  setIsLoading(false)
                }
              )
            )
          }
        } else {
          setIsLoading(false)
          quickCheckoutCalledRef.current = false
          dispatch(removeQuickCheckoutTicketsData())
          getQuickCheckoutData()
        }
      } else {
        setIsLoading(false)
        firstResponse?.errorMessage &&
          showErrorToast(firstResponse?.errorMessage)
        if (!firstResponse.errorMessage) {
          quickCheckoutCalledRef.current = false
          getQuickCheckoutData()
        }
      }
    }
  }

  const itemDataMap = useItemDataMap()

  useEffect(() => {
    if (!itemDataMap?.size || !categoryData.data?.length) return
    // Map item data by itemId for faster lookups

    const getVariantsAndPrice = (itemId: string) => {
      const isItemExist = itemDataMap.get(itemId)
      if (!isItemExist)
        return {
          variants: null,
          price: 0,
          pricePerLb: 0,
          minLbs: 0,
          pieces: 0,
          cleaningService: '',
          cleaningServices: {},
        }
      let variants = null
      let price = 0
      let pricePerLb = 0
      let minLbs = 0
      let pieces = 0
      let cleaningService = ''
      let cleaningServices = {}
      if (selectedCustomerData?.assignedPriceListId) {
        const priceList = isItemExist.prices?.find(
          (price: ItemPrices) =>
            price.priceListId === selectedCustomerData.assignedPriceListId
        )
        if (priceList) {
          variants = priceList.variants?.length ? priceList.variants : null
          price = parseFloat(priceList.basePrice) || 0
          pricePerLb = parseFloat(priceList.pricePerLb || '0')
          minLbs = parseInt(priceList.minLbs || '0')
          pieces = parseInt(isItemExist?.cleaning?.pieces || '0')
          cleaningService = isItemExist?.cleaning?.defaultService || ''
          cleaningServices = priceList.cleaningServices || {}
        }
      } else {
        variants = isItemExist.prices[0]?.variants?.length
          ? isItemExist.prices[0].variants
          : null
        price = parseFloat(isItemExist.prices[0]?.basePrice) || 0
        pricePerLb = parseFloat(isItemExist.prices[0].pricePerLb || '0')
        minLbs = parseInt(isItemExist.prices[0].minLbs || '0')
        pieces = parseInt(isItemExist?.cleaning?.pieces || '0')
        cleaningService = isItemExist?.cleaning?.defaultService || ''
        cleaningServices = isItemExist.prices[0].cleaningServices || {}
      }

      return {
        variants,
        price,
        pricePerLb,
        minLbs,
        pieces,
        cleaningService,
        cleaningServices,
      }
    }

    const categoryMainData = categoryData.data.map((category) => {
      const items = category.items
        ?.map((item) => {
          const {
            variants,
            price,
            pricePerLb,
            minLbs,
            pieces,
            cleaningService,
            cleaningServices,
          } = getVariantsAndPrice(item.itemId)
          const isItemExist = itemDataMap.get(item.itemId)
          return {
            ...item,
            name: isItemExist ? isItemExist.name : '',
            variants,
            price,
            itemType: isItemExist ? isItemExist.itemType : '',
            pricePerLb: isItemExist ? pricePerLb : 0,
            minLbs: isItemExist ? minLbs : 0,
            pieces,
            cleaningService,
            cleaningServices,
          }
        })
        ?.sort((a, b) => (b.isFavorite ? 1 : 0) - (a.isFavorite ? 1 : 0))

      return { name: category.name, items }
    })

    dispatch(setCategoryPosData(categoryMainData))
  }, [dispatch, itemDataMap, categoryData, selectedCustomerData])

  useEffect(() => {
    if (currentTicketState.selectedItem !== null) {
      const mainItemData = itemsData?.data?.find(
        (value) => value.itemId === currentTicketState?.selectedItem?.itemId
      )
      let assignedVariants: VariantItemData[] = []
      let selectedVariantData: SelectedVariantData[] = []
      let isRequired = false
      const validIndex = mainItemData?.prices.findIndex((price) => {
        return price.priceListId === customerData?.assignedPriceListId
      })

      if (mainItemData && validIndex !== undefined && validIndex !== -1) {
        mainItemData.prices[validIndex]?.variants?.forEach((value) => {
          const findVariantData = variantData?.data?.find(
            (val) => value.id === val.variantId
          )
          if (findVariantData) {
            if (!isRequired && findVariantData.isRequired) {
              isRequired = true
            }
            let addOnPrices = {}
            findVariantData.addOns?.forEach((val) => {
              if (value.addOnPrices[val] !== -1) {
                addOnPrices = {
                  ...addOnPrices,
                  [val]: value.addOnPrices[val] ? value.addOnPrices[val] : 0,
                }
              }
            })
            if (Object.keys(addOnPrices)?.length > 0) {
              assignedVariants = [
                ...assignedVariants,
                {
                  id: value?.id as string,
                  addOnPrices: addOnPrices,
                  isMultiple: findVariantData.isMultiple ?? false,
                  isRequired: findVariantData.isRequired ?? false,
                  name: findVariantData.name,
                  orderIndex: findVariantData.orderIndex,
                  isValid: !findVariantData.isRequired || false,
                  isPreset: findVariantData.isPreset || false,
                },
              ]
            }
            if (
              currentTicketState?.selectedItem?.selectFrom === TICKET &&
              !findVariantData.isMultiple &&
              currentTicketState?.selectedItemIndex !== null
            ) {
              const currentSublines =
                currentTicketData?.currentTicket?.lineItems[
                  currentTicketState?.selectedItemIndex
                ]?.sublines
              currentSublines?.forEach((value) => {
                if (value.addOnName && value.variantId) {
                  const isVariantExist = findVariantData.addOns?.includes(
                    value?.addOnName
                  )
                  if (isVariantExist) {
                    selectedVariantData = [
                      ...selectedVariantData,
                      { variantId: value.variantId, addOn: value.addOnName },
                    ]
                  }
                }
              })
            }
          }
        })
      }
      assignedVariants?.sort((a, b) => a.orderIndex - b.orderIndex)
      let requiredVariantsCount: number | null = null
      if (
        currentTicketState.selectedItem.selectFrom === TICKET &&
        currentTicketState.selectedItemIndex !== null &&
        currentTicketData.currentTicket?.lineItems &&
        currentTicketData.currentTicket?.lineItems.length > 0
      ) {
        const updateItemQuantityByIndex = findItemIndexBySelectedIndex(
          currentTicketData.currentTicket?.lineItems,
          currentTicketState.selectedItemIndex as number
        )
        if (updateItemQuantityByIndex !== -1) {
          const currentSublines =
            currentTicketData.currentTicket?.lineItems[
              updateItemQuantityByIndex
            ].sublines
          assignedVariants = updateAllAssignedVariants(
            currentSublines || [],
            assignedVariants
          )
        }
      }
      requiredVariantsCount = checkRequiredVariantsNumber(assignedVariants)
      dispatch(
        setRequiredVariants(
          requiredVariantsCount > 0 ? requiredVariantsCount : null
        )
      )
      dispatch(setAssignedVariantData(assignedVariants))
      if (currentTicketState.selectedItem.selectFrom === TICKET) {
        dispatch(setVariantModalOpen(true))
      }
      if (requiredVariantsCount) {
        dispatch(setIsButtonDisabled(true))
      } else {
        dispatch(setIsButtonDisabled(false))
      }
      dispatch(setIsItemSelected(true))
      dispatch(setSelectedItemIndex(currentTicketState.selectedItem.index))
      dispatch(
        setTotalLineItem(
          currentTicketData.currentTicket?.lineItems?.length || 0
        )
      )
    }
    // eslint-disable-next-line
  }, [
    dispatch,
    currentTicketState.selectedItem,
    currentTicketState.categoryPosData,
    variantData.data,
    customerData,
  ])

  useEffect(() => {
    if (
      currentTicketState.assignedVariantData &&
      currentTicketState.assignedVariantData?.length > 0
    ) {
      const requiredVariantsCount = checkRequiredVariantsNumber(
        currentTicketState.assignedVariantData
      )
      dispatch(
        setRequiredVariants(
          currentTicketState.isItemSelected
            ? requiredVariantsCount
            : requiredVariantsCount > 0
              ? requiredVariantsCount
              : null
        )
      )
    }
    // eslint-disable-next-line
  }, [dispatch, currentTicketState.assignedVariantData])

  const clearEmptySublines = useCallback(() => {
    if (currentTicketState.selectedItemIndex !== null) {
      const lineItems = structuredClone(
        currentTicketData?.currentTicket?.lineItems
      )
      if (lineItems && lineItems.length > 0) {
        const updateItemSublinesByIndex = findItemIndexBySelectedIndex(
          lineItems,
          currentTicketState.selectedItemIndex
        )

        const updatedSublinesIndexData = lineItems[
          updateItemSublinesByIndex
        ]?.sublines
          ?.map((subline, sublineIndex) => {
            return (subline.type === SublineType.CUSTOM &&
              (subline.comment !== '' || subline.price !== null)) ||
              subline.type === SublineType.VARIANT
              ? -1
              : subline.isPreSet
                ? -1
                : sublineIndex
          })
          ?.filter((value) => value !== -1)

        const updatedSubline = lineItems[
          updateItemSublinesByIndex
        ]?.sublines?.filter((val) => {
          return (
            (val.type === SublineType.CUSTOM &&
              (val.comment !== '' || val.price !== null)) ||
            val.type === SublineType.VARIANT
          )
        })
        if (
          lineItems[updateItemSublinesByIndex]?.sublines &&
          lineItems[updateItemSublinesByIndex]?.sublines?.length > 0
        ) {
          lineItems[updateItemSublinesByIndex].sublines = updatedSubline
        }
        dispatch(
          addOrEditCurrentTicketData(
            updateAmounts(
              { ...currentTicketData?.currentTicket, lineItems },
              discountType
            ),
            EDIT_TICKET_ACTION_TYPE.CLEAR_EMPTY_SUBLINE,
            {
              lineItemIndex: updateItemSublinesByIndex,
              sublinesIndex: updatedSublinesIndexData,
            },
            (res) => {
              if (res) {
                dispatch(setLinItemState(LineItemState.Blurred))
              }
            }
          )
        )
      }
    }
  }, [
    currentTicketState.selectedItemIndex,
    currentTicketData,
    dispatch,
    discountType,
  ])

  const scrollToBottom = useCallback((childIndex: number) => {
    const targetElement = document.getElementById(
      `itemdata-${childIndex}`
    ) as HTMLElement
    if (!targetElement) return
    targetElement.scrollIntoView({ behavior: 'smooth', block: 'end' })
  }, [])

  const scrollHandler = useCallback(
    (index: number) => {
      setTimeout(() => {
        scrollToBottom(index)
      }, 300)
    },
    [scrollToBottom]
  )

  useEffect(() => {
    if (
      currentTicketState.totalLineItem > 0 &&
      typeof currentTicketState.selectedItem?.index === 'number'
    ) {
      const delay = currentTicketState?.isItemSelected ? 1000 : 200
      const moveIndex = currentTicketState?.selectedItem?.index as number
      setTimeout(() => {
        scrollToBottom(moveIndex)
      }, delay)
    }
  }, [
    currentTicketState?.totalLineItem,
    currentTicketState.isItemSelected,
    currentTicketState.selectedItem,
    scrollToBottom,
  ])

  const scrollToNextRequired = useCallback(() => {
    const requiredElements = document.querySelectorAll('.required')
    if (requiredElements.length === 0) return

    const parentElement = document.querySelector('.addon-sale-pos-height')
    const headerOffset = 140

    if (!parentElement) return

    const firstElement = requiredElements[0] as HTMLElement
    const firstElementPosition = firstElement.offsetTop
    const firstOffsetPosition = firstElementPosition - headerOffset

    parentElement.scrollTo({
      top: firstOffsetPosition,
      behavior: 'smooth',
    })
  }, [])

  const handleClickShowMore = useCallback(() => {
    setIsShowMore((prevState) => !prevState)
  }, [])

  const handleClickDelete = useCallback(() => {
    setIsShowMore(false)
  }, [])

  const handleClickSaveEdit = useCallback(() => {
    setIsShowMore(false)
  }, [])

  useGlobalScrollEvent(isProgrammaticScroll)

  const handleClickCancel = useCallback(() => {
    if (currentTicketState?.requiredVariants === 0) {
      setIsShowMore(false)
      dispatch(setIsItemSelected(false))
      dispatch(setAssignedVariantData([]))
      dispatch(setSelectedItem(null))
      dispatch(setSelectedItemIndex(null))
      dispatch(setQuantityModalOpen(false))
      dispatch(setLinItemState(LineItemState.Blurred))
    }
  }, [dispatch, currentTicketState?.requiredVariants])

  const isPayAndCheckoutButton = useMemo(() => {
    if (
      ticketsData?.quickCheckoutTicket &&
      ticketsData?.quickCheckoutTicket?.length > 0
    ) {
      const selectedTicketData = ticketsData?.quickCheckoutTicket?.filter(
        (value) => value.isSelected
      )
      return selectedTicketData.some((ticket) =>
        isButtonEnabled(ticket.ticketStatus as TicketStatusForFilter)
      )
    }
    return false
  }, [ticketsData?.quickCheckoutTicket])

  const isPayLater = useMemo(() => {
    if (
      ticketsData?.quickCheckoutTicket &&
      ticketsData?.quickCheckoutTicket?.length > 0
    ) {
      const selectedTicketData = ticketsData?.quickCheckoutTicket?.filter(
        (value) => value.isSelected
      )
      return !selectedTicketData.some(
        (ticket) => ticket.ticketStatus === TicketStatusForFilter.CLOSED
      )
    }
    return true
  }, [ticketsData])

  const salesComponent = useMemo(
    () => (
      <Sales
        key={translation.SALES_KEY}
        isShowMore={isShowMore}
        onScrollToRequired={scrollToBottom}
        saleRef={saleRef}
        clearEmptySublines={clearEmptySublines}
        pathName={pathName}
        executeIfEditable={executeIfEditable}
        isProgrammaticScroll={isProgrammaticScroll}
        getQuickCheckoutData={getQuickCheckoutData}
        quickCheckoutCalledRef={quickCheckoutCalledRef}
      />
    ),
    [
      isShowMore,
      scrollToBottom,
      saleRef,
      clearEmptySublines,
      pathName,
      executeIfEditable,
      getQuickCheckoutData,
    ]
  )

  const setPayableAmountData = useCallback(() => {
    if (totalAmount > 0) {
      setPayableAmount(
        calculatePayableAmount(currentPaymentIncentive, totalAmount)
      )
    }
  }, [currentPaymentIncentive, totalAmount])

  const quickCheckoutComponent = useMemo(
    () => (
      <QuickCheckout
        mode={PosLayoutMode.QUICK_CHECKOUT}
        key={translation.QUICK_CHECKOUT_KEY}
        setPayableAmountData={setPayableAmountData}
        totalAmount={totalAmount}
        fetchMoreData={() => {
          quickCheckoutCalledRef.current = false
          getQuickCheckoutData(true)
        }}
        dataLength={ticketsData?.quickCheckoutTicket?.length}
        isButtonDisabled={isNextButtonDisabled}
      />
    ),
    [
      totalAmount,
      setPayableAmountData,
      getQuickCheckoutData,
      ticketsData,
      isNextButtonDisabled,
    ]
  )

  const variantBoxComponent = useMemo(
    () => (
      <VariantBox
        variantRef={variantRef}
        isShowMore={isShowMore}
        requiredVariantsRef={saleRef}
        onClick={handleClickShowMore}
        onClickCancel={handleClickCancel}
        isProgrammaticScroll={isProgrammaticScroll}
        executeIfEditable={executeIfEditable}
      />
    ),
    [
      variantRef,
      isShowMore,
      saleRef,
      handleClickShowMore,
      handleClickCancel,
      executeIfEditable,
    ]
  )

  const currentTicketComponent = useMemo(
    () => (
      <CurrentTicket
        saleRef={saleRef}
        variantRef={variantRef}
        onClickDelete={handleClickDelete}
        handleClickSaveEdit={handleClickSaveEdit}
        onScrollToRequired={scrollToNextRequired}
        clearEmptySublines={clearEmptySublines}
        scrollToBottom={scrollHandler}
        executeIfEditable={executeIfEditable}
        actualTicketData={actualTicketData}
      />
    ),
    [
      saleRef,
      variantRef,
      handleClickDelete,
      handleClickSaveEdit,
      scrollToNextRequired,
      clearEmptySublines,
      scrollHandler,
      executeIfEditable,
      actualTicketData,
    ]
  )

  const componentsOrder = useMemo(() => {
    if (storeValue && !storeValue.data) return []
    if (storeValue?.data?.posLayout === PosLayout.TICKET_POS) {
      return [
        { component: currentTicketComponent },
        {
          component: (
            <div className="sales-container ml-14">
              {salesComponent}
              {variantBoxComponent}
            </div>
          ),
        },
      ]
    } else {
      return [
        {
          component: (
            <div className="sales-container mr-14">
              {salesComponent}
              {variantBoxComponent}
            </div>
          ),
        },
        { component: currentTicketComponent },
      ]
    }
  }, [storeValue, currentTicketComponent, salesComponent, variantBoxComponent])

  const posElements = useMemo(() => {
    if (storeValue.loading || !storeValue.data) return []

    return storeValue?.data?.posLayout === PosLayout.POS_TICKET
      ? [
          {
            component: (
              <div className={'mr-14 sales-container'}>{salesComponent}</div>
            ),
          },
          { component: quickCheckoutComponent },
        ]
      : [
          { component: quickCheckoutComponent },
          {
            component: (
              <div className={'ml-14 sales-container'}>{salesComponent}</div>
            ),
          },
        ]
  }, [storeValue, salesComponent, quickCheckoutComponent])

  const elements = useMemo(() => {
    return pathName === appRoutes.dashboard ? posElements : componentsOrder
  }, [componentsOrder, pathName, posElements])

  return (
    <>
      <Box
        className="d-flex remaining-space position-relative overflow-x-hidden"
        as="div"
      >
        {!storeValue.loading &&
          storeValue.data &&
          elements?.map(({ component }) => component)}

        {pathName === appRoutes.dashboard && (
          <PaymentModalWrapper
            amount={totalAmount}
            amountCash={totalAmount}
            cardPaymentAmount={totalAmount}
            amountCard={totalAmount}
            payableAmount={payableAmount}
            handlePaidAmount={(amount, undefined, isPayOnly) => {
              handlePaidAmount(amount, isPayOnly || false)
              if (isPayOnly) {
                dispatch(setIsPayOnly(isPayOnly))
              }
            }}
            isCheck={isCheck}
            paidValue={paidValue || 0}
            paymentType={paymentType as PaymentMethodType}
            totalAmount={totalAmount}
            updateIsCheck={(value: boolean) => {
              setIsCheck(value)
            }}
            updatePayableAmount={(value?: number) => {
              setPayableAmount(value)
            }}
            currentPaymentIncentive={currentPaymentIncentive}
            loading={isLoading}
            isPayLater={isPayLater}
            isPayAndCheckoutButton={isPayAndCheckoutButton}
            isCheckout={isCheckout}
            updateCheckout={(value: boolean) => {
              setIsCheckout(value)
            }}
            updatePaidValue={(value?: number) => {
              setPaidValue(value)
            }}
            mode={PosLayoutMode.QUICK_CHECKOUT}
          />
        )}
      </Box>
    </>
  )
}

export default TicketNdSales
