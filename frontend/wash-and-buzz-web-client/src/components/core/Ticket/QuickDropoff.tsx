import { default as nextDisableIcon } from '@/../public/images/charge-checkout-disabled.svg'
import { default as nextIcon } from '@/../public/images/charge-checkout-enabled.svg'
import { ThemeButton } from '@/components/core/Button/Button'
import { PhoneKeypadInput } from '@/components/core/PhoneKeypad/PhoneKeypadInput'
import { FulfillmentModal } from '@/components/core/PopupModals/FulfillmentModal'
import { FulfillmentMethod } from '@/components/Fulfillment/FulfillmentMethodButton'
import { useTicketPrint } from '@/hooks/useTicketPrint'
import { modifyTaxValue } from '@/serializer/store.serializer'
import { addTicketsData } from '@/store/actions/ticket.action'
import { TaxRates, TaxSale } from '@/types/module/taxSaleModule'
import {
  EMPTY_CURRENT_TICKET_DATA,
  TicketAction,
} from '@/types/module/ticketNdSalesModule'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { quickDropoffKeyPadButtons } from '@/utils/constant'
import { translation } from '@/utils/translation'
import { FC, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'

interface QuickDropoffProps {
  onClickKeyPad: (item: string) => void
  onChange: (item: string) => void
  onClear: () => void
  handleButtonClick: (value: boolean) => void
  value: string
  disabled: boolean
  openFulfillmentModal: boolean
  setActiveTab: React.Dispatch<React.SetStateAction<string>>
  getQuickCheckoutData: (fetchMoreData?: boolean) => void
  quickCheckoutCalledRef: React.MutableRefObject<boolean>
  setOpenFulfillmentModal: React.Dispatch<React.SetStateAction<boolean>>
}

export const QuickDropoff: FC<QuickDropoffProps> = ({
  onClickKeyPad,
  onClear,
  onChange,
  value,
  disabled = true,
  handleButtonClick,
  openFulfillmentModal,
  setActiveTab,
  getQuickCheckoutData,
  quickCheckoutCalledRef,
  setOpenFulfillmentModal,
}) => {
  const [loading, setLoading] = useState<boolean>(false)
  const dispatch = useDispatch()
  const { handlePrintReceipt } = useTicketPrint()
  const storeValue = useSelector((state: MainStoreType) => state.storeData)
  const teamMemberData = useSelector(
    (state: MainStoreType) => state.teamMemberData
  )
  const discountType = useSelector((state: MainStoreType) => {
    if (Array.isArray(state.taxSaleData.data)) {
      const taxSaleData = state.taxSaleData.data
      return taxSaleData.find((taxSale: TaxSale) => taxSale.isSelected)?.name ==
        'NET_SALES'
        ? 'PRE_TAX'
        : 'POST_TAX'
    }
    return 'PRE_TAX'
  })
  return (
    <>
      <div className="quick-drop-container p-20">
        <div></div>
        <PhoneKeypadInput
          onClickKeyPad={onClickKeyPad}
          onClear={onClear}
          onChange={(e) => {
            onChange(e?.target?.value)
          }}
          value={value}
          label={`${translation?.ITEM_QUANTITY}*`}
          labelVariant="Primary16Medium20"
          labelSx={{ color: '#303030' }}
          keyPadContainer="user-pin-modal-box col-lg-5 col-xl-4"
          inputWrapperClass="col-lg-6 col-xl-5"
          wrapperClass="quick-drop-key-pad-container"
          data={quickDropoffKeyPadButtons}
          keyPadTextVariant="Primary16Medium80"
          name="quantity"
          autoFocus
          keyPadSx={{ background: 'tertiary' }}
        />
        <ThemeButton
          disabled={disabled}
          className="print-edit-btn-ticket-box"
          text={translation.NEXT}
          icon={disabled ? nextDisableIcon : nextIcon}
          onClick={() => {
            handleButtonClick(true)
          }}
        />
      </div>

      {openFulfillmentModal && (
        <FulfillmentModal
          modalContainer="fulfillment-modal"
          isOpen={openFulfillmentModal}
          defaultSelectedMethod={FulfillmentMethod.DELIVERY}
          onClose={() => {
            handleButtonClick(false)
          }}
          title={translation.FULFILLMENT_MODAL}
          loading={loading}
          isQuickDropoff
          handleSave={(paidAmount, fulfillment) => {
            setLoading(true)
            dispatch(
              addTicketsData(
                {
                  ...EMPTY_CURRENT_TICKET_DATA,
                  notes: value,
                  fulfillment: fulfillment,
                  flagged: true,
                  lineItems: [],
                  createTeamMemberId: teamMemberData?.selectedTeamMember
                    ?.teamMemberId as string,
                  taxConfiguration: {
                    rates: modifyTaxValue(
                      storeValue.data?.taxRates as TaxRates
                    ),
                    calculationBase: discountType,
                  },
                },
                undefined,
                (res, data) => {
                  if (res) {
                    quickCheckoutCalledRef.current = false
                    getQuickCheckoutData()
                    setActiveTab(TicketAction.CREATE_TICKET)
                    handlePrintReceipt(data)
                    onChange('')
                    setOpenFulfillmentModal(false)
                    setLoading(false)
                  }
                }
              )
            )
          }}
          showCloseIcon
          handlePrepay={() => {}}
        />
      )}
    </>
  )
}
