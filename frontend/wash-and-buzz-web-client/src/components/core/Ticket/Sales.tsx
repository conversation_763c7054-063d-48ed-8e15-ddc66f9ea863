import { ThemeButton } from '@/components/core/Button/Button'
import { PhoneKeypadModal } from '@/components/core/PhoneKeypadModal/PhoneKeypadModal'
import {
  default as CommonTab,
  default as CommonTabTab,
} from '@/components/core/Tabs/CommonTab'
import { updateAmounts } from '@/components/core/Ticket/Calculations'
import { QuickDropoff } from '@/components/core/Ticket/QuickDropoff'
import useEditTicket from '@/hooks/useEditTicket'
import useIsVariantRequirementFulfilled from '@/hooks/useRequiredVariantsCheck'
import { setFailedPermission } from '@/store/actions/common.action'
import {
  setInitialRender,
  setQuantityForItem,
  setQuantityModalOpen,
  setRequiredVariants,
  setSelectedItem,
  setSelectedItemIndex,
  setSelectedPriceAdjustmentIndex,
  setTotalLineItem,
  setVariantModalOpen,
  setWeightModalOpen,
} from '@/store/actions/currentTicketState.action'
import {
  addOrEditCurrentTicketData,
  setCreditMemoButtonState,
  setCustomDiscountButtonState,
  setDebitMemoButtonState,
  setNoteState,
  setPriceAdjustmentButtonState,
  setSalesButtonState,
} from '@/store/actions/ticket.action'
import { CategoryPosItemsData } from '@/types/module/categoryModule'
import { TextSale } from '@/types/module/commonModule'
import { ItemSelection, MemoValue } from '@/types/module/itemModule'
import {
  OperationType,
  PhoneKeyPadState,
  ValidationType,
} from '@/types/module/phoneKeyPadModule'
import {
  PriceAdjustment,
  PriceAdjustmentType,
} from '@/types/module/priceAdjustmentModule'
import { TaxSale } from '@/types/module/taxSaleModule'
import {
  ButtonState,
  EDIT_TICKET_ACTION_TYPE,
  ItemType,
  LineItem,
  NoteState,
  SalesProps,
  TicketAction,
} from '@/types/module/ticketNdSalesModule'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { ComponentAccess } from '@/utils/componentAccess'
import {
  POS_PRICE_ADJUSTMENT_CREDIT_MEMO,
  POS_PRICE_ADJUSTMENT_CUSTOM_DISCOUNT,
  POS_PRICE_ADJUSTMENT_DEBIT_MEMO,
} from '@/utils/constant'
import {
  checkPermission,
  findItemIndexBySelectedIndex,
  getItemPrice,
  removeSymbolsAndLetters,
  safeHandleAction,
} from '@/utils/functions'
import { iconLibrary } from '@/utils/iconLibrary'
import { appRoutes } from '@/utils/routes'
import { checkProductHasAccess } from '@/utils/strings'
import { translation } from '@/utils/translation'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import React, {
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useMediaQuery } from 'react-responsive'
import { Element } from 'react-scroll'
import { Box, Card, Divider, Flex, Text } from 'theme-ui'
import { v4 as uuidv4 } from 'uuid'

const p2Tabs = [
  {
    label: TicketAction.CREATE_TICKET,
    key: TicketAction.CREATE_TICKET,
  },
]

const p3Tabs = [
  ...p2Tabs,
  {
    label: TicketAction.QUICK_DROP_OFF,
    key: TicketAction.QUICK_DROP_OFF,
  },
]
const Sales: FC<SalesProps> = ({
  onScrollToRequired,
  isShowMore,
  pathName,
  executeIfEditable,
  isProgrammaticScroll,
  getQuickCheckoutData,
  quickCheckoutCalledRef,
}) => {
  const salesTabContainerRef = useRef<HTMLDivElement | null>(null)
  const salesContentRefs = useRef<{ [key: string]: HTMLDivElement | null }>({})
  const route = useRouter()
  const dispatch = useDispatch()
  const [keypadState, setKeypadState] = useState<PhoneKeyPadState>({
    isKeypadOpen: false,
  })
  const [selectedItemData, setSelectedItemData] =
    useState<CategoryPosItemsData>()
  const [selectedPriceAdjustmentData, setSelectedPriceAdjustmentData] =
    useState<PriceAdjustment>()
  const isEditTicket = useEditTicket()
  const [activeTab, setActiveTab] = useState<string>(TicketAction.CREATE_TICKET)
  const [openFulfillmentModal, setOpenFulfillmentModal] =
    useState<boolean>(false)
  const [quickDropoffValue, setQuickDropoffValue] = useState<string>('')
  const [carouselActiveTab, setCarouselActiveTab] = useState<string>('')
  const commonData = useSelector((state: MainStoreType) => state.commonData)
  const teamMemberData = useSelector(
    (state: MainStoreType) => state.teamMemberData
  )
  const isVariantRequirementFulfilled = useIsVariantRequirementFulfilled()
  const currentTicketState = useSelector(
    (state: MainStoreType) => state.currentTicketState
  )
  const itemsData = useSelector((state: MainStoreType) => state.itemData)
  const storeData = useSelector((state: MainStoreType) => state.storeData)
  const currentTicketData = useSelector(
    (state: MainStoreType) => state.ticketsData
  )
  const priceAdjustment = useSelector(
    (state: MainStoreType) => state.priceAdjustmentData
  )
  const permissionData = useSelector(
    (state: MainStoreType) => state.permissionsData
  )
  const discountType = useSelector((state: MainStoreType) => {
    if (Array.isArray(state.taxSaleData.data)) {
      const taxSaleData = state.taxSaleData.data
      return taxSaleData.find((taxSale: TaxSale) => taxSale.isSelected)?.name ==
        'NET_SALES'
        ? 'PRE_TAX'
        : 'POST_TAX'
    }
    return 'PRE_TAX'
  })

  const getItemQuantity = (itemId: string, itemType: ItemType): number => {
    let quantity = 0
    currentTicketData?.currentTicket?.lineItems
      ?.filter((val) => val.itemId === itemId)
      ?.forEach((val) => {
        quantity += itemType === TextSale.WEIGHTED ? 1 : val.quantity ?? 0
      })
    return quantity
  }

  const getPriceAdjustmentQuantity = (adjustmentId: number): number => {
    const quantity = currentTicketData?.currentTicket?.priceAdjustments?.filter(
      (val) => val.priceAdjustmentId === adjustmentId
    )?.length
    return quantity ?? 0
  }

  const getCustomDiscountQuantity = (): number => {
    const quantity = currentTicketData?.currentTicket?.priceAdjustments?.filter(
      (val) => val.name === translation.CUSTOM_DISCOUNT
    )?.length
    return quantity ?? 0
  }

  const getMemoQuantity = (
    type: OperationType.CREDIT | OperationType.DEBIT
  ): number => {
    const quantity = currentTicketData?.currentTicket?.memos?.filter(
      (val) => val.type === type
    )?.length
    return quantity ?? 0
  }

  const addPriceAdjustment = useCallback(
    (sale: PriceAdjustment) => {
      const existingLineItem =
        structuredClone(currentTicketData?.currentTicket?.priceAdjustments) ||
        []
      const newPriceAdjustment = {
        priceAdjustmentId: sale.priceAdjustmentId,
        type: sale.type,
        name: sale.name,
        rate:
          sale.type === PriceAdjustmentType.DISCOUNT
            ? -sale.amount / 100
            : sale.amount / 100, // TODO: We should change the Operations screen to store the rate as a [-1, 1] number.
        subtotal: 0,
        tax: 0,
        comment: '',
        priceAdjustmentIndex: existingLineItem?.length,
      }

      const priceAdjustments = [
        ...(currentTicketData.currentTicket?.priceAdjustments || []),
        newPriceAdjustment,
      ]
      dispatch(
        addOrEditCurrentTicketData(
          updateAmounts(
            {
              ...currentTicketData?.currentTicket,
              priceAdjustments: priceAdjustments,
            },
            discountType
          ),
          isEditTicket ? EDIT_TICKET_ACTION_TYPE.ADD_PRICE_ADJUSTMENT : null,
          null
        )
      )
      dispatch(setSelectedPriceAdjustmentIndex(null))
      dispatch(setPriceAdjustmentButtonState(null))
      dispatch(setNoteState(null))
    },
    [currentTicketData.currentTicket, discountType, dispatch, isEditTicket]
  )

  const handleClickKeyPad = (text: string | number) => {
    const textValue = text.toString()
    setQuickDropoffValue(removeSymbolsAndLetters(quickDropoffValue + textValue))
  }

  const handleChangeKeyPad = (text: string) => {
    setQuickDropoffValue(removeSymbolsAndLetters(text))
  }

  const handleClear = () => {
    const numericValue = removeSymbolsAndLetters(quickDropoffValue).slice(0, -1)
    setQuickDropoffValue(numericValue)
  }

  useEffect(() => {
    if (salesTabContainerRef.current) {
      // Select the active tab button

      const activeTabElement = salesTabContainerRef.current.querySelector(
        `.item-library-tab-btn.active`
      ) as HTMLElement

      if (activeTabElement) {
        // Scroll the active tab into view

        setTimeout(() => {
          activeTabElement.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'center',
          })
        }, 1000)
      }
    }
  }, [carouselActiveTab])

  const handleSalesTabScroll = () => {
    if (isProgrammaticScroll.current) {
      return
    }

    const container = document.getElementById('containerElements')

    if (!container) return

    let closestTab = ''
    let smallestDistance = Infinity
    Object.keys(salesContentRefs.current).forEach((key) => {
      const element = salesContentRefs.current[key]
      if (element) {
        const elementRect = element.getBoundingClientRect()
        const distanceFromTop = Math.abs(
          elementRect.top - container.getBoundingClientRect().top
        )
        if (distanceFromTop < smallestDistance) {
          smallestDistance = distanceFromTop
          closestTab = key
        }
      }
    })

    if (closestTab && closestTab !== carouselActiveTab) {
      setCarouselActiveTab(closestTab)
    }
  }

  const getTextValue = useCallback(
    (itemType: ItemType, isEdit: boolean) => {
      let textValue = 0
      if (itemType === TextSale.SALES) {
        textValue = isEdit
          ? (currentTicketData?.currentTicket?.taxConfiguration?.rates?.[
              TextSale.SALES?.toUpperCase()
            ] as number)
          : (storeData.data?.taxRates?.sales as number)
      } else if (itemType === TextSale.WEIGHTED) {
        textValue = isEdit
          ? (currentTicketData?.currentTicket?.taxConfiguration?.rates?.[
              TextSale.WEIGHTED?.toUpperCase()
            ] as number)
          : (storeData.data?.taxRates?.weighted as number)
      } else if (itemType === TextSale.CLEANING) {
        textValue = isEdit
          ? (currentTicketData?.currentTicket?.taxConfiguration?.rates?.[
              TextSale.CLEANING?.toUpperCase()
            ] as number)
          : (storeData.data?.taxRates?.cleaning as number)
      } else if (itemType === TextSale.MAID) {
        textValue = isEdit
          ? (currentTicketData?.currentTicket?.taxConfiguration?.rates?.[
              TextSale.MAID?.toUpperCase()
            ] as number)
          : (storeData.data?.taxRates?.maid as number)
      } else {
        textValue = isEdit
          ? (currentTicketData?.currentTicket?.taxConfiguration?.rates?.[
              TextSale.OTHER?.toUpperCase()
            ] as number)
          : (storeData.data?.taxRates?.other as number)
      }

      return isEdit ? textValue : textValue / 100
    },
    [currentTicketData?.currentTicket?.taxConfiguration?.rates, storeData.data]
  )

  const updateKeypadState = useCallback(
    (
      isKeypadOpen: boolean,
      selectedOperation:
        | OperationType.DEBIT
        | OperationType.CREDIT
        | OperationType.CUSTOM_DISCOUNT,
      validationType: ValidationType.integer | ValidationType.percentage,
      title: string
    ) => {
      setKeypadState({
        ...keypadState,
        isKeypadOpen,
        selectedOperation,
        validationType,
        title,
      })
    },
    [keypadState]
  )

  useEffect(() => {
    if (commonData.data.failedPermission) {
      if (
        commonData.data.failedPermission ===
        POS_PRICE_ADJUSTMENT_CUSTOM_DISCOUNT
      ) {
        updateKeypadState(
          true,
          OperationType.CUSTOM_DISCOUNT,
          ValidationType.percentage,
          translation.CUSTOM_DISCOUNT
        )
      } else if (
        commonData.data.failedPermission === POS_PRICE_ADJUSTMENT_CREDIT_MEMO
      ) {
        updateKeypadState(
          true,
          OperationType.CREDIT,
          ValidationType.integer,
          translation.CREDIT_MEMO
        )
      } else {
        updateKeypadState(
          true,
          OperationType.DEBIT,
          ValidationType.integer,
          translation.DEBIT_MEMO
        )
      }
    }
    // eslint-disable-next-line
  }, [commonData.data.failedPermission])

  const addCustomDiscount = useCallback(() => {
    if (
      checkPermission(
        permissionData.data || [],
        POS_PRICE_ADJUSTMENT_CUSTOM_DISCOUNT,
        teamMemberData.selectedTeamMember?.positionId
      )
    ) {
      updateKeypadState(
        true,
        OperationType.CUSTOM_DISCOUNT,
        ValidationType.percentage,
        translation.CUSTOM_DISCOUNT
      )
      dispatch(setNoteState(null))
      dispatch(setCustomDiscountButtonState(null))
    }
  }, [
    permissionData.data,
    teamMemberData.selectedTeamMember?.positionId,
    updateKeypadState,
    dispatch,
  ])

  const addCreditMemo = useCallback(() => {
    if (
      checkPermission(
        permissionData.data || [],
        POS_PRICE_ADJUSTMENT_CREDIT_MEMO,
        teamMemberData.selectedTeamMember?.positionId
      )
    ) {
      updateKeypadState(
        true,
        OperationType.CREDIT,
        ValidationType.integer,
        translation.CREDIT_MEMO
      )
      dispatch(setNoteState(null))
      dispatch(setCreditMemoButtonState(null))
    }
  }, [
    permissionData.data,
    teamMemberData.selectedTeamMember?.positionId,
    updateKeypadState,
    dispatch,
  ])

  const addDebitMemo = useCallback(() => {
    if (
      checkPermission(
        permissionData.data || [],
        POS_PRICE_ADJUSTMENT_DEBIT_MEMO,
        teamMemberData.selectedTeamMember?.positionId
      )
    ) {
      updateKeypadState(
        true,
        OperationType.DEBIT,
        ValidationType.integer,
        translation.DEBIT_MEMO
      )
      dispatch(setNoteState(null))
      dispatch(setDebitMemoButtonState(null))
    }
  }, [
    permissionData.data,
    teamMemberData.selectedTeamMember?.positionId,
    updateKeypadState,
    dispatch,
  ])

  const onKeypadSubmit = (value: number) => {
    const existingPriceAdjustments =
      structuredClone(currentTicketData?.currentTicket?.priceAdjustments) || []

    if (keypadState.selectedOperation === OperationType.CUSTOM_DISCOUNT) {
      const newPriceAdjustment = {
        priceAdjustmentId: 0,
        type: PriceAdjustmentType.DISCOUNT,
        name: translation.CUSTOM_DISCOUNT,
        rate: -value / 100,
        subtotal: 0,
        tax: 0,
        comment: '',
        priceAdjustmentIndex: existingPriceAdjustments?.length,
      }

      const priceAdjustments = [
        ...(structuredClone(
          currentTicketData.currentTicket?.priceAdjustments
        ) || []),
        newPriceAdjustment,
      ]

      dispatch(
        addOrEditCurrentTicketData(
          updateAmounts(
            {
              ...currentTicketData?.currentTicket,
              priceAdjustments,
            },
            discountType
          ),
          isEditTicket ? EDIT_TICKET_ACTION_TYPE.ADD_PRICE_ADJUSTMENT : null,
          null
        )
      )
    } else if (
      keypadState.selectedOperation === OperationType.DEBIT ||
      keypadState.selectedOperation === OperationType.CREDIT
    ) {
      const existingLineItem = [
        ...(structuredClone(currentTicketData?.currentTicket?.memos) || []),
      ]
      const translationKey =
        keypadState.selectedOperation === OperationType.DEBIT
          ? MemoValue.DEBIT_MEMO
          : MemoValue.CREDIT_MEMO
      const newMemo = {
        type: keypadState.selectedOperation,
        name: translation[translationKey],
        subtotal:
          keypadState.selectedOperation === OperationType.DEBIT
            ? value
            : -value,
        comment: '',
        memoIndex: existingLineItem.length,
      }

      const updatedMemos = [
        ...(structuredClone(currentTicketData?.currentTicket?.memos) || []),
        newMemo,
      ]

      dispatch(
        addOrEditCurrentTicketData(
          updateAmounts(
            {
              ...currentTicketData?.currentTicket,
              memos: updatedMemos,
            },
            discountType
          ),
          isEditTicket ? EDIT_TICKET_ACTION_TYPE.ADD_MEMO : null,
          null
        )
      )
    }
    dispatch(setFailedPermission(''))
    setKeypadState((state) => ({ ...state, isKeypadOpen: false }))
  }

  const getLineItem = useCallback(
    (value: CategoryPosItemsData, itemIndex: number): LineItem => {
      return {
        itemId: value.itemId,
        name: value.name,
        quantity: 1,
        itemType: value.itemType,
        price: getItemPrice(value),
        tax: 0,
        subtotal: 0,
        sublines: [],
        taxRate: getTextValue(
          value.itemType,
          currentTicketData?.currentTicket?.isEdit || false
        ),
        isSelected: true,
        isQuantityModalOpen: true,
        uniqueId: uuidv4(),
        itemIndex: itemIndex,
        pricePerLb: value.itemType === TextSale.WEIGHTED ? value.pricePerLb : 0,
        minLbs: value.itemType === TextSale.WEIGHTED ? value.minLbs : 0,
        cleaningService:
          value.itemType === TextSale.CLEANING ? value.cleaningService : '',
        pieces: value.itemType === TextSale.CLEANING ? value.pieces : 0,
      }
    },
    [currentTicketData?.currentTicket?.isEdit, getTextValue]
  )

  const handleSelectCategoryItems = useCallback(
    (value: CategoryPosItemsData) => {
      if (value.itemType === TextSale.WEIGHTED) {
        dispatch(setWeightModalOpen(true))
      } else {
        dispatch(setQuantityModalOpen(true))
      }
      dispatch(setInitialRender(true))
      dispatch(setVariantModalOpen(true))
      if (isVariantRequirementFulfilled) {
        const existingLineItem =
          structuredClone(currentTicketData?.currentTicket?.lineItems) || []
        const updateItemQuantityByIndex = findItemIndexBySelectedIndex(
          existingLineItem,
          currentTicketState.selectedItemIndex as number
        )
        if (
          updateItemQuantityByIndex !== -1 &&
          currentTicketState.selectedItemIndex !== null
        ) {
          existingLineItem[updateItemQuantityByIndex] = {
            ...existingLineItem[updateItemQuantityByIndex],
            isSelected: false,
            isQuantityModalOpen: false,
          }
        }
        const isItemExist = existingLineItem?.findLastIndex(
          (item) => item.itemId === value.itemId
        )
        isItemExist !== -1
          ? existingLineItem.splice(
              isItemExist + 1,
              0,
              getLineItem(value, existingLineItem?.length)
            )
          : existingLineItem.push(getLineItem(value, existingLineItem?.length))

        dispatch(
          setSelectedItem({
            itemId: value.itemId,
            name: value.name,
            selectFrom: ItemSelection.SALE,
            index: existingLineItem?.length - 1,
          })
        )
        dispatch(
          addOrEditCurrentTicketData(
            updateAmounts(
              {
                ...currentTicketData?.currentTicket,
                lineItems: existingLineItem,
              },
              discountType
            ),
            isEditTicket ? EDIT_TICKET_ACTION_TYPE.ADD_LINE_ITEM : null,
            {
              lineItemIndex:
                isItemExist !== -1
                  ? isItemExist + 1
                  : existingLineItem?.length - 1,
            }
          )
        )
        const totalLineItem = currentTicketData?.currentTicket?.lineItems
          ? currentTicketData?.currentTicket?.lineItems?.length + 1
          : currentTicketData?.currentTicket === null
            ? 1
            : 0
        dispatch(setTotalLineItem(totalLineItem))
        dispatch(setSelectedItemIndex(null))
        dispatch(setRequiredVariants(null))
        dispatch(setQuantityForItem(1))
        const moveIndex =
          isItemExist !== -1 ? isItemExist + 1 : existingLineItem.length - 1
        if (onScrollToRequired) {
          onScrollToRequired(moveIndex)
        }
        if (pathName === appRoutes.dashboard) {
          route.push(appRoutes.pos)
        }
      }
      dispatch(setNoteState(null))
      dispatch(setSalesButtonState(null))
    },
    [
      currentTicketData?.currentTicket,
      currentTicketState.selectedItemIndex,
      discountType,
      dispatch,
      getLineItem,
      isEditTicket,
      isVariantRequirementFulfilled,
      onScrollToRequired,
      pathName,
      route,
    ]
  )

  useEffect(() => {
    if (
      !currentTicketData ||
      currentTicketData.salesButtonState !== ButtonState.Clicked
    )
      return

    if (currentTicketData.noteState !== NoteState.Focused && selectedItemData) {
      handleSelectCategoryItems(selectedItemData)
    }
  }, [currentTicketData, handleSelectCategoryItems, selectedItemData])

  useEffect(() => {
    if (
      !currentTicketData ||
      currentTicketData.priceAdjustmentButtonState !== ButtonState.Clicked
    )
      return

    if (
      currentTicketData.noteState !== NoteState.Focused &&
      selectedPriceAdjustmentData
    ) {
      addPriceAdjustment(selectedPriceAdjustmentData)
    }
  }, [currentTicketData, addPriceAdjustment, selectedPriceAdjustmentData])

  useEffect(() => {
    if (
      !currentTicketData ||
      currentTicketData.customDiscountButtonState !== ButtonState.Clicked
    )
      return

    if (currentTicketData.noteState !== NoteState.Focused) {
      addCustomDiscount()
    }
  }, [currentTicketData, addCustomDiscount])

  useEffect(() => {
    if (
      !currentTicketData ||
      currentTicketData.creditMemoButtonState !== ButtonState.Clicked
    )
      return

    if (currentTicketData.noteState !== NoteState.Focused) {
      addCreditMemo()
    }
  }, [currentTicketData, addCreditMemo])

  useEffect(() => {
    if (
      !currentTicketData ||
      currentTicketData.debitMemoButtonState !== ButtonState.Clicked
    )
      return

    if (currentTicketData.noteState !== NoteState.Focused) {
      addDebitMemo()
    }
  }, [currentTicketData, addDebitMemo])

  const isSmallScreen = useMediaQuery({
    query: '(min-height: 700px) and (max-height:740px)',
  })
  const isMediumSmallScreen = useMediaQuery({
    query: '(min-height: 741px) and (max-height:800px)',
  })

  const tabs = useMemo(() => {
    const tabs =
      currentTicketState?.categoryPosData
        ?.filter((tab) => tab?.items?.length > 0)
        ?.map((tab) => ({
          label: tab.name,
          key: tab.name,
        })) || []
    // TODO : Restore Price Adjustments tab when we need to enable price adjustment buttons (see PR 1347)
    return tabs
  }, [currentTicketState])

  const categoryPosData = useMemo(
    () =>
      currentTicketState?.categoryPosData
        ?.filter((tab) => tab?.items?.length > 0)
        ?.map((tab) => tab),
    [currentTicketState]
  )

  useEffect(() => {
    if (currentTicketState?.categoryPosData.length > 0) {
      setCarouselActiveTab(
        currentTicketState?.categoryPosData?.[0]?.name.toString().toLowerCase()
      )
    }
  }, [currentTicketState?.categoryPosData])

  const memoizedTabs = useMemo(() => {
    return (
      <div
        ref={salesTabContainerRef}
        className="tabs-wrapper w-100 overflow-x-auto scrollbar-hide px-20"
      >
        <CommonTab
          key={'sales-common-tabs'}
          tabs={tabs}
          activeTab={carouselActiveTab}
          checkIsLowerCase={true}
          customWrapperClass={`${pathName === appRoutes.dashboard ? 'mt-14' : 'mt-0'} item-library-tabs-container w-100 overflow-x-auto scrollbar-hide mb-14`}
          customTabClass="item-library-tab-btn"
          setActiveTab={setCarouselActiveTab}
          scrollMode
        />
      </div>
    )
  }, [tabs, carouselActiveTab, pathName]) // Add dependencies here

  return (
    <>
      <motion.div
        initial={{
          height: '100%',
          maxHeight:
            pathName === appRoutes.dashboard
              ? '100%'
              : isSmallScreen
                ? '49%'
                : isMediumSmallScreen
                  ? '47%'
                  : '58%',
        }}
        animate={{
          height: isShowMore ? '0px' : '-webkit-fill-available',
          opacity: isShowMore ? 0 : 1,
        }}
        transition={{ duration: 0.6 }}
      >
        <Card
          className={`position-relative item-list-box-2 ${pathName === appRoutes.dashboard ? 'pb-20' : 'py-20'}`}
        >
          {currentTicketState.categoryPosData &&
          currentTicketState.categoryPosData?.length > 0 ? (
            <>
              {pathName === appRoutes.dashboard && (
                <CommonTabTab
                  tabs={
                    checkProductHasAccess(
                      ComponentAccess.p2,
                      storeData.data?.posProductType as number
                    )
                      ? p2Tabs
                      : checkProductHasAccess(
                            ComponentAccess.p3,
                            storeData.data?.posProductType as number
                          )
                        ? p3Tabs
                        : []
                  }
                  activeTab={activeTab}
                  checkIsLowerCase={false}
                  setActiveTab={(tab: string) => {
                    setActiveTab(tab)
                  }}
                  customWrapperClass={`mt-0 w-100 pos-tab-container ${storeData.data?.posProductType === 2 ? 'br-10' : 'pos-tab-container-br'}`}
                  customTabClass="webkit-fill-available p-20 pos-tab-btn"
                  textVariant="Primary18Medium18"
                />
              )}
              {activeTab === TicketAction.CREATE_TICKET && (
                <>
                  {memoizedTabs}
                  <Element
                    name="salesContainerElement"
                    className={`${pathName === appRoutes.dashboard ? 'calculated-state-height-sales-scroll' : 'default-state-height-sales-scroll'} overflow-y-auto`}
                    id="containerElements"
                    onScroll={handleSalesTabScroll}
                  >
                    <Card
                      className={`px-20`}
                      sx={{ overflowY: 'auto', bg: 'white' }}
                    >
                      <Box>
                        {categoryPosData?.map((category, index) => (
                          <React.Fragment
                            key={`category-${category.name}-${index}`}
                          >
                            <Flex
                              sx={{
                                gap: '8px',
                                mt: '6px',
                              }}
                              className="w-100"
                            >
                              <Element
                                key={`categoryData-${category.name}-${index}`}
                                name={`${category.name?.toLowerCase()}-${index}`}
                                id={`categoryData-${category.name}-${index}`}
                              >
                                <Box
                                  sx={{
                                    display: 'inline-flex',
                                    gap: '8px',
                                    flexWrap: 'wrap',
                                  }}
                                  ref={(el) => {
                                    if (el instanceof HTMLDivElement) {
                                      salesContentRefs.current[
                                        category.name.toLocaleLowerCase()
                                      ] = el
                                    }
                                  }}
                                >
                                  {category?.items?.map(
                                    (value, categoryIndex) => {
                                      const matchingItemData =
                                        itemsData?.data?.find(
                                          (item) => item.itemId === value.itemId
                                        )
                                      const iconKey =
                                        matchingItemData?.iconLibraryKey
                                      const iconPath = iconKey
                                        ? iconLibrary.getIcon(iconKey)
                                        : undefined
                                      const isIconExist = iconPath

                                      return (
                                        <ThemeButton
                                          key={`categoryIndex-${value.itemId}-${categoryIndex}`}
                                          variant="salePosBorderSquareBtn"
                                          className="flex-column align-items-center justify-content-center"
                                          isSquareButton
                                          sx={{
                                            color: 'primaryText',
                                            fontFamily: 'notoSans',
                                            boxShadow:
                                              category?.name?.toLowerCase() ===
                                              carouselActiveTab
                                                ? 'unset'
                                                : '0px 0px 3px 0px rgba(0, 0, 0, 0.20)',
                                            borderColor:
                                              category?.name?.toLowerCase() ===
                                              carouselActiveTab
                                                ? ''
                                                : 'transparent',
                                          }}
                                          textSx={{
                                            whiteSpace: 'normal',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            mt: isIconExist ? '4px' : 0,
                                          }}
                                          textVariant="Secondary14Medium116"
                                          icon={isIconExist ? iconPath : ''}
                                          iconClassName="pos-display-icons me-0"
                                          text={value.name}
                                          disabled={
                                            !isVariantRequirementFulfilled
                                          }
                                          onClick={safeHandleAction(
                                            (
                                              event: React.MouseEvent<HTMLButtonElement>
                                            ) => {
                                              event.stopPropagation()
                                              executeIfEditable(() => {
                                                dispatch(
                                                  setSalesButtonState(
                                                    ButtonState.Clicked
                                                  )
                                                )
                                                setSelectedItemData(value)
                                              })
                                            },
                                            currentTicketState.isItemDisabled
                                          )}
                                          quantity={getItemQuantity(
                                            value.itemId,
                                            value.itemType
                                          )}
                                          id={`${value.name?.toLowerCase()}-${index}`}
                                        />
                                      )
                                    }
                                  )}
                                </Box>
                              </Element>
                            </Flex>
                            <Divider
                              color={
                                index != categoryPosData.length - 1
                                  ? '#EAEAEA'
                                  : '#FFFFFF'
                              }
                              opacity={2}
                            />
                          </React.Fragment>
                        ))}
                      </Box>
                      <Flex
                        sx={{
                          gap: '8px',
                          mt: '8px',
                          mb: '5px',
                          display: 'none !important', // TODO : Remove this line when we need to enable price adjustment buttons (see PR 1347)
                        }}
                        className="w-100"
                      >
                        <Element
                          key={`categoryData-${translation?.PRICE_ADJUSTMENT}-${categoryPosData.length}`}
                          name={`${translation?.PRICE_ADJUSTMENT?.toLowerCase()}-${categoryPosData.length}`}
                          id={`categoryData-${translation?.PRICE_ADJUSTMENT}-${categoryPosData.length}`}
                        >
                          <Box
                            sx={{
                              display: 'inline-flex',
                              gap: '8px',
                              flexWrap: 'wrap',
                            }}
                            ref={(el) => {
                              if (el instanceof HTMLDivElement) {
                                salesContentRefs.current[
                                  translation?.PRICE_ADJUSTMENT?.toLowerCase()
                                ] = el
                              }
                            }}
                          >
                            {Array.isArray(priceAdjustment?.data) &&
                              priceAdjustment?.data?.map(
                                (sale: PriceAdjustment, index: number) => (
                                  <ThemeButton
                                    key={`priceAdjustment-${sale.id}-${index}`}
                                    variant="salePosBorderSquareBtn"
                                    isSquareButton
                                    sx={{
                                      color: 'primaryText',
                                      fontFamily: 'notoSans',
                                      boxShadow:
                                        translation?.PRICE_ADJUSTMENT?.toLowerCase() ===
                                        carouselActiveTab
                                          ? 'unset'
                                          : '0px 0px 3px 0px rgba(0, 0, 0, 0.20)',
                                      borderColor:
                                        translation?.PRICE_ADJUSTMENT?.toLowerCase() ===
                                        carouselActiveTab
                                          ? ''
                                          : 'transparent',
                                    }}
                                    textSx={{
                                      whiteSpace: 'normal',
                                      overflow: 'hidden',
                                      textOverflow: 'ellipsis',
                                    }}
                                    textVariant="Secondary14Medium142"
                                    text={sale.name}
                                    onClick={safeHandleAction(
                                      (
                                        event: React.MouseEvent<HTMLButtonElement>
                                      ) => {
                                        event.stopPropagation()
                                        executeIfEditable(() => {
                                          setSelectedPriceAdjustmentData(sale)
                                          dispatch(
                                            setPriceAdjustmentButtonState(
                                              ButtonState.Clicked
                                            )
                                          )
                                        }, true)
                                      },
                                      currentTicketState.isItemDisabled
                                    )}
                                    quantity={
                                      sale.priceAdjustmentId
                                        ? getPriceAdjustmentQuantity(
                                            sale.priceAdjustmentId
                                          )
                                        : 0
                                    }
                                  />
                                )
                              )}
                            <ThemeButton
                              variant="salePosBorderSquareBtn"
                              isSquareButton
                              sx={{
                                color: 'primaryText',
                                fontFamily: 'notoSans',
                                boxShadow:
                                  translation?.PRICE_ADJUSTMENT?.toLowerCase() ===
                                  carouselActiveTab
                                    ? 'unset'
                                    : '0px 0px 3px 0px rgba(0, 0, 0, 0.20)',
                                borderColor:
                                  translation?.PRICE_ADJUSTMENT?.toLowerCase() ===
                                  carouselActiveTab
                                    ? ''
                                    : 'transparent',
                                display: 'none !important', // TODO : Remove this line when we need to enable price adjustment buttons (see PR 1347)
                              }}
                              textSx={{
                                whiteSpace: 'normal',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                              }}
                              textVariant="Secondary14Medium142"
                              text={translation.CUSTOM_DISCOUNT}
                              onClick={safeHandleAction(
                                (
                                  event: React.MouseEvent<HTMLButtonElement>
                                ) => {
                                  event.stopPropagation()
                                  executeIfEditable(() => {
                                    dispatch(
                                      setCustomDiscountButtonState(
                                        ButtonState.Clicked
                                      )
                                    )
                                  }, true)
                                },
                                currentTicketState.isItemDisabled
                              )}
                              quantity={getCustomDiscountQuantity()}
                              disabled={!isVariantRequirementFulfilled}
                            />
                            <ThemeButton
                              variant="salePosBorderSquareBtn"
                              isSquareButton
                              sx={{
                                color: 'primaryText',
                                fontFamily: 'notoSans',
                                boxShadow:
                                  translation?.PRICE_ADJUSTMENT?.toLowerCase() ===
                                  carouselActiveTab
                                    ? 'unset'
                                    : '0px 0px 3px 0px rgba(0, 0, 0, 0.20)',
                                borderColor:
                                  translation?.PRICE_ADJUSTMENT?.toLowerCase() ===
                                  carouselActiveTab
                                    ? ''
                                    : 'transparent',
                                display: 'none !important', // TODO : Remove this line when we need to enable the debit memo button
                              }}
                              textSx={{
                                whiteSpace: 'normal',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                              }}
                              textVariant="Secondary14Medium142"
                              text={translation.DEBIT_MEMO}
                              onClick={safeHandleAction(
                                (
                                  event: React.MouseEvent<HTMLButtonElement>
                                ) => {
                                  event.stopPropagation()
                                  dispatch(
                                    setDebitMemoButtonState(ButtonState.Clicked)
                                  )
                                },
                                currentTicketState.isItemDisabled
                              )}
                              quantity={getMemoQuantity(OperationType.DEBIT)}
                              disabled={!isVariantRequirementFulfilled}
                            />
                            <ThemeButton
                              variant="salePosBorderSquareBtn"
                              isSquareButton
                              sx={{
                                color: 'primaryText',
                                fontFamily: 'notoSans',
                                boxShadow:
                                  translation?.PRICE_ADJUSTMENT?.toLowerCase() ===
                                  carouselActiveTab
                                    ? 'unset'
                                    : '0px 0px 3px 0px rgba(0, 0, 0, 0.20)',
                                borderColor:
                                  translation?.PRICE_ADJUSTMENT?.toLowerCase() ===
                                  carouselActiveTab
                                    ? ''
                                    : 'transparent',
                                display: 'none !important', // TODO : Remove this line when we need to enable the credit memo button
                              }}
                              textSx={{
                                whiteSpace: 'normal',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                              }}
                              textVariant="Secondary14Medium142"
                              text={translation.CREDIT_MEMO}
                              onClick={safeHandleAction(
                                (
                                  event: React.MouseEvent<HTMLButtonElement>
                                ) => {
                                  event.stopPropagation()
                                  dispatch(
                                    setCreditMemoButtonState(
                                      ButtonState.Clicked
                                    )
                                  )
                                },
                                currentTicketState.isItemDisabled
                              )}
                              quantity={getMemoQuantity(OperationType.CREDIT)}
                              disabled={!isVariantRequirementFulfilled}
                            />
                          </Box>
                        </Element>
                      </Flex>
                    </Card>
                  </Element>
                </>
              )}
              {activeTab === TicketAction.QUICK_DROP_OFF && (
                <QuickDropoff
                  onClickKeyPad={handleClickKeyPad}
                  onChange={handleChangeKeyPad}
                  onClear={handleClear}
                  value={quickDropoffValue}
                  openFulfillmentModal={openFulfillmentModal}
                  handleButtonClick={(value: boolean) => {
                    setOpenFulfillmentModal(value)
                  }}
                  setActiveTab={setActiveTab}
                  disabled={
                    quickDropoffValue === '' || Number(quickDropoffValue) <= 0
                  }
                  getQuickCheckoutData={getQuickCheckoutData}
                  quickCheckoutCalledRef={quickCheckoutCalledRef}
                  setOpenFulfillmentModal={setOpenFulfillmentModal}
                />
              )}
            </>
          ) : (
            <Flex
              sx={{
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
              }}
            >
              <Text className="text-center" variant="Secondary14Medium88">
                {translation.ITEM_AND_PRICE_LIST_BEFORE} &quot;
                <Link href={appRoutes.itemsPriceList}>
                  <Text className="clickable-text-only">
                    {translation.ITEM_AND_PRICE_LIST}
                  </Text>
                </Link>
                &quot; {translation.ITEM_AND_PRICE_LIST_AFTER}
              </Text>
            </Flex>
          )}
          <PhoneKeypadModal
            title={keypadState.title || ''}
            isOpen={keypadState.isKeypadOpen}
            numberValidationType={keypadState.validationType}
            onClose={() => {
              dispatch(setFailedPermission(''))
              setKeypadState((state) => ({ ...state, isKeypadOpen: false }))
            }}
            modalContainer="change-user"
            onSubmit={onKeypadSubmit}
          />
        </Card>
      </motion.div>
    </>
  )
}
export default Sales
