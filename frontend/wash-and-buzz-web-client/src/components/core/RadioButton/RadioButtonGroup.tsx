import { Flex, Text } from 'theme-ui'

export const RadioButtonGroup = ({
  options,
  selectedValue,
  onChange,
  name,
}: {
  options: { label: string; value: string }[]
  selectedValue: string
  onChange: (value: string) => void
  name: string
}) => (
  <Flex sx={{ flexDirection: 'column', gap: 20 }}>
    {options.map(({ label, value }) => (
      <div className="radio-item-ticket-filter" key={value}>
        <input
          type="radio"
          id={value}
          name={name}
          checked={value === selectedValue}
          value={value}
          onChange={(e) => onChange(e.target.value)}
        />
        <label htmlFor={value} />
        <Text
          variant="Primary16Regular20"
          className="font-noto-sans"
          sx={{ ml: '8px', mt: '2px' }}
        >
          {label}
        </Text>
      </div>
    ))}
  </Flex>
)
