'use client'
import { translation } from '@/utils/translation'
import React, { useEffect, useState } from 'react'
import { Box, Text } from 'theme-ui'
import RevenueSummaryDataLayout from './RevenueSummaryDataLayout'
import SalesAnalysisDateRange from '../sales-analysis/SalesAnalysisDateRange'
import { GetAnalyticsReportsApi } from '@/types/module/salesReportsModule'
import { usePathname, useSearchParams } from 'next/navigation'
import { useDispatch } from 'react-redux'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import leftArrow from '@/../public/images/arrow-left.svg'
import AnalyticsDataPage from '../analytics-overview-page'
import { getSalesReportsData } from '@/store/actions/salesReports.action'
import Spinner from '@/components/spinner/Spinner'
import { useSelector } from 'react-redux'
import { MainStoreType } from '@/types/store/reducers/main.reducers'

const RevenueSummaryPage = () => {
  const searchParam = useSearchParams()
  const getParam = searchParam.get('type')
  const dispatch = useDispatch()
  const router = useRouter()
  const pathname = usePathname()
  const { loading } = useSelector(
    (state: MainStoreType) => state?.salesReportsData
  )
  const [isAnalyticsPage, setIsAnalyticsPage] = useState<boolean>(false)
  const [analyticsPageApiData, setAnalyticsPageApiData] =
    useState<GetAnalyticsReportsApi | null>(null)

  useEffect(() => {
    const keyCheck = [
      translation.SALES_GROSS,
      translation.SALES_NET,
      translation.SALES_TOTAL,
    ]
    if (getParam !== null && keyCheck.includes(getParam)) {
      dispatch(getSalesReportsData())
      setIsAnalyticsPage(true)
    } else {
      setIsAnalyticsPage(false)
    }
    // eslint-disable-next-line
  }, [getParam])
  return (
    <div className="row g-0">
      <Box
        className={`py-30 px-20 custom-scroll ${!isAnalyticsPage ? 'col-xxl-9 mx-auto' : 'col-12'}`}
      >
        {!isAnalyticsPage ? (
          <>
            <Text as="p" pb="30px" variant="Primary24Demi28">
              {translation.REVENUE_SUMMARY}
            </Text>
          </>
        ) : (
          <Text
            as="p"
            pb="30px"
            variant="Primary24Demi28"
            onClick={() => {
              router.replace(pathname)
            }}
          >
            <Image
              className="cursor-pointer"
              src={leftArrow}
              height={30}
              width={30}
              alt={'icon'}
              onClick={() => setIsAnalyticsPage(false)}
            />
          </Text>
        )}

        <SalesAnalysisDateRange
          setAnalyticsPageApiData={setAnalyticsPageApiData}
        />

        <Box sx={{ mt: '30px' }}>
          {!isAnalyticsPage ? (
            <>
              <Text as="p" pb="30px" variant="Primary18Demi111">
                {translation.REVENUE_SUMMARY_CASH_BASED}
              </Text>
              <RevenueSummaryDataLayout
                setIsAnalyticsPage={setIsAnalyticsPage}
                setAnalyticsPageApiData={setAnalyticsPageApiData}
              />
            </>
          ) : (
            <AnalyticsDataPage analyticsPageApiData={analyticsPageApiData} />
          )}
        </Box>
        <Spinner visible={loading} />
      </Box>
    </div>
  )
}

export default RevenueSummaryPage
