import { ThemeButton } from '@/components/core/Button/Button'
import { AnalyticsOverviewProps } from '@/types/module/salesReportsModule'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { CURRENCY_FORMATTER } from '@/utils/currency'
import { translation } from '@/utils/translation'
import React from 'react'
import { useSelector } from 'react-redux'
import { Box, Divider, Text } from 'theme-ui'
import { useRouter } from 'next/navigation'

interface RevenueData {
  label: string
  amount: number
  isClickable: boolean
  onClick?: (type: string) => void
  type?: string
}

const RevenueSummaryDataLayout: React.FC<AnalyticsOverviewProps> = ({
  setIsAnalyticsPage,
  setAnalyticsPageApiData,
}) => {
  const revenueSummaryData = useSelector(
    (state: MainStoreType) => state?.salesReportsData?.data
  )
  const router = useRouter()

  const revenueData: RevenueData[] = [
    {
      label: translation.GROSS_SALES,
      amount: revenueSummaryData?.sales?.gross as number,
      isClickable: true,
      onClick: (type: string) => commonHandler(type), // onClick for navigation to analytics page
      type: translation.SALES_GROSS,
    },
    {
      label: translation.DISCOUNT_AND_COMPS,
      amount: revenueSummaryData?.sales?.discountTotal as number,
      isClickable: false,
    },
    {
      label: translation.NET_SALES,
      amount: revenueSummaryData?.sales?.net as number,
      isClickable: false,
      onClick: () => {},
      type: translation.SALES_NET,
    },
    {
      label: translation.TAX,
      amount: revenueSummaryData?.sales?.taxTotal as number,
      isClickable: true,
      onClick: (type: string) => commonHandler(type),
    },
    {
      label: translation.TIP,
      amount: 0, // Tips are not being sent from the API, so set this to $0 for now
      isClickable: true,
      onClick: (type: string) => commonHandler(type),
    },
    {
      label: translation.TOTAL,
      amount: revenueSummaryData?.sales?.total as number,
      isClickable: true,
      onClick: (type: string) => commonHandler(type),
      type: translation.SALES_TOTAL,
    },
  ]

  const commonHandler = (type: string) => {
    const expectData = {
      startDate: revenueSummaryData?.startDate as string,
      endDate: revenueSummaryData?.endDate as string,
      dateRangeType: revenueSummaryData?.dateRangeType as string,
      type: type as string,
    }

    setAnalyticsPageApiData(expectData)
    setIsAnalyticsPage(true)
    router.push(`?type=${type}`)
  }

  return (
    <Box>
      {revenueData.map((item: RevenueData, index: number) => (
        <React.Fragment key={index}>
          <div className="row justify-content-between g-0 pb-10">
            <div className="col-4">
              <Text variant="NotoSans16Medium125">{item.label}</Text>
            </div>
            <div className="col-3 sales-data-amount">
              <ThemeButton
                onClick={() =>
                  item?.onClick && item?.onClick(item?.type as string)
                }
                variant={item.isClickable ? 'clickableTxtBtn' : 'onlyText'}
                className={`font-noto-sans ${item.isClickable ? 'clickable-text-only' : 'only-text cursor-default'}`}
                text={CURRENCY_FORMATTER.format(item.amount ?? 0)}
              />
            </div>
          </div>
          <Divider
            className="mb-20 mt-0"
            sx={{ color: '#000', height: '1px' }}
          />
        </React.Fragment>
      ))}
    </Box>
  )
}

export default RevenueSummaryDataLayout
