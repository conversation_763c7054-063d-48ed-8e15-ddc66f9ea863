'use client'
import { translation } from '@/utils/translation'
import Image from 'next/image'
import { Paragraph, Text } from 'theme-ui'
import {
  AnalyticsOverviewProps,
  SalesSummaryOverviewData,
} from '@/types/module/salesReportsModule'
import CleaningItemType from '@/../public/images/dry-cleaning-item-type.svg'
import SalesItemTypeIcon from '@/../public/images/Sales-item-type.svg'
import MaidItemTypeIcon from '@/../public/images/maid-item-type.svg'
import CleaningWeightItemType from '@/../public/images/cleaning-weight-item-type.svg'
import others from '@/../public/images/others-menu.svg'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { useSelector } from 'react-redux'
import React from 'react'
import { useRouter } from 'next/navigation'
import { CURRENCY_FORMATTER } from '@/utils/currency'
import { checkProductHasAccess } from '@/utils/strings'
import { ComponentAccess } from '@/utils/componentAccess'

export const SalesByItemTypes: React.FC<AnalyticsOverviewProps> = ({
  setIsAnalyticsPage,
  setAnalyticsPageApiData,
}) => {
  const router = useRouter()
  const salesByItemTypes = useSelector(
    (state: MainStoreType) => state?.salesReportsData.data
  )
  const storeData = useSelector((state: MainStoreType) => state?.storeData.data)

  const salesByItemTypesData: SalesSummaryOverviewData[] = [
    {
      amount: salesByItemTypes?.itemType?.cleaning as number,
      label: translation.CLEANING,
      icon: CleaningItemType,
      type: translation.ITEMTYPE_CLEANING,
      access: checkProductHasAccess(
        ComponentAccess.salesAnalysisCleaningItemType,
        storeData?.posProductType as number
      ),
    },
    {
      amount: salesByItemTypes?.itemType?.weighted as number,
      label: translation.CLEANING_WEIGHT,
      icon: CleaningWeightItemType,
      type: translation.ITEMTYPE_CLEANING_WEIGHTED,
      access: checkProductHasAccess(
        ComponentAccess.salesAnalysisCleaningWeightItemType,
        storeData?.posProductType as number
      ),
    },
    {
      amount: salesByItemTypes?.itemType?.sales as number,
      label: translation.SALES,
      icon: SalesItemTypeIcon,
      type: translation.ITEMTYPE_SALES,
      access: checkProductHasAccess(
        ComponentAccess.salesAnalysisSalesItemType,
        storeData?.posProductType as number
      ),
    },
    {
      amount: salesByItemTypes?.itemType?.maid as number,
      label: translation.MAID,
      icon: MaidItemTypeIcon,
      type: translation.ITEMTYPE_MAID,
      access: checkProductHasAccess(
        ComponentAccess.salesAnalysisMaidItemType,
        storeData?.posProductType as number
      ),
    },
    {
      amount: salesByItemTypes?.itemType?.other as number,
      label: translation.OTHERS,
      icon: others,
      type: translation.ITEMTYPE_OTHER,
      access: checkProductHasAccess(
        ComponentAccess.salesAnalysisOthersItemType,
        storeData?.posProductType as number
      ),
    },
  ]

  return (
    <>
      <Text as="p" variant="Primary18Demi111" className="pt-30 pb-20">
        {translation.SALES_BY_ITEM_TYPES}
      </Text>
      <div className="row g-0">
        {salesByItemTypesData
          ?.filter((setting) => setting.access)
          ?.map((data: SalesSummaryOverviewData, index: number) => (
            <div
              key={index}
              className="m-05 col-3 cursor-pointer overview-boxes"
              onClick={() => {
                const expectData = {
                  startDate: salesByItemTypes?.startDate as string,
                  endDate: salesByItemTypes?.endDate as string,
                  dateRangeType: salesByItemTypes?.dateRangeType as string,
                  type: data.type as string,
                }

                setAnalyticsPageApiData(expectData)
                setIsAnalyticsPage(true)
                router.push(`?type=${data.type}`)
              }}
            >
              <div className="row g-0 justify-content-between align-items-center py-20">
                <div className="col-3 pl-20">
                  <Image src={data.icon} alt="icon" height={40} width={40} />
                </div>
                <div className="col-9 pl-20">
                  <Paragraph variant="NotoSans16Medium125">
                    {CURRENCY_FORMATTER.format(data.amount ?? 0)}
                  </Paragraph>
                  <Paragraph pt="5px" variant="NotoSans16Medium125">
                    {data.label}
                  </Paragraph>
                </div>
              </div>
            </div>
          ))}
      </div>
    </>
  )
}
