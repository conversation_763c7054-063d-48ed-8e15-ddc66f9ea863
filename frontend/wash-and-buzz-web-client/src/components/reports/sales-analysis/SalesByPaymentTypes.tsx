'use client'
import React from 'react'
import { translation } from '@/utils/translation'
import Image from 'next/image'
import { Paragraph, Text } from 'theme-ui'
import {
  AnalyticsOverviewProps,
  SalesSummaryOverviewData,
} from '@/types/module/salesReportsModule'
import { useSelector } from 'react-redux'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import cashIcon from '@/../public/images/cash-reports.svg'
import cardIcon from '@/../public/images/credit-card-reports.svg'
import checkIcon from '@/../public/images/payment-type-check-icon.svg'
import storeCreditIcon from '@/../public/images/payment-type-store credit.svg'
import otherIcon from '@/../public/images/payment-type-other.svg'
import { useRouter } from 'next/navigation'
import { CURRENCY_FORMATTER } from '@/utils/currency'

export const SalesByPaymentTypes: React.FC<AnalyticsOverviewProps> = ({
  setIsAnalyticsPage,
  setAnalyticsPageApiData,
}) => {
  const router = useRouter()
  const salesByPaymentData = useSelector(
    (state: MainStoreType) => state?.salesReportsData.data
  )

  const salesByPaymentTypesData: SalesSummaryOverviewData[] = [
    {
      amount: salesByPaymentData?.paymentType?.cash as number,
      label: translation.CASH,
      icon: cashIcon,
      type: translation.PAYMENT_CASH,
    },
    {
      amount: salesByPaymentData?.paymentType?.creditCard as number,
      label: translation.CARD,
      icon: cardIcon,
      type: translation.PAYMENT_CREDITCARD,
    },
    {
      amount: salesByPaymentData?.paymentType?.check as number,
      label: translation.CHECK,
      icon: checkIcon,
      type: translation.PAYMENT_CHECK,
    },
    {
      amount: salesByPaymentData?.paymentType?.storeCredit as number,
      label: translation.STORE_CREDIT,
      icon: storeCreditIcon,
      type: translation.PAYMENT_STORE_CREDIT,
    },
    {
      amount: salesByPaymentData?.paymentType?.other as number,
      label: translation.OTHERS,
      icon: otherIcon,
      type: translation.PAYMENT_OTHER,
    },
  ]
  return (
    <>
      <Text as="p" variant="Primary18Demi111" className="pt-30 pb-20">
        {translation.SALES_BY_PAYMENT_TYPES}
      </Text>
      <div className="row g-0">
        {salesByPaymentTypesData.map(
          (data: SalesSummaryOverviewData, index: number) => (
            <div
              key={index}
              className="m-05 col-3 cursor-pointer overview-boxes"
              onClick={() => {
                const expectData = {
                  startDate: salesByPaymentData?.startDate as string,
                  endDate: salesByPaymentData?.endDate as string,
                  dateRangeType: salesByPaymentData?.dateRangeType as string,
                  type: data.type as string,
                }
                setAnalyticsPageApiData(expectData)
                setIsAnalyticsPage(true)
                router.push(`?type=${data.type}`)
              }}
            >
              <div className="row g-0 justify-content-between align-items-center py-20">
                <div className="col-3 pl-20">
                  <Image src={data.icon} alt="icon" height={40} width={40} />
                </div>
                <div className="col-9 pl-20">
                  <Paragraph variant="NotoSans16Medium125">
                    {CURRENCY_FORMATTER.format(data.amount ?? 0)}
                  </Paragraph>
                  <Paragraph pt="5px" variant="NotoSans16Medium125">
                    {data.label}
                  </Paragraph>
                </div>
              </div>
            </div>
          )
        )}
      </div>
    </>
  )
}
