.assign-rack-table {
  border-collapse: collapse;
  width: 100%;
  table-layout: fixed;
  border-spacing: 0 0.2em;
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.assign-rack-table thead {
  display: table;
  width: 100%;
  position: sticky;
  top: 0;
  background-color: transparent !important;
  z-index: 10;
  margin-bottom: 8px !important;
}

.assign-rack-table thead th {
  font-weight: 600;
  padding: 10px;
  text-align: left;
  background-color: transparent !important;
}

.assign-rack-table thead th:nth-child(-n + 2),
.assign-rack-table tbody td:nth-child(-n + 2) {
  text-align: left;
}

.assign-rack-table tbody {
  width: 100%;
}

.assign-rack-table tbody tr {
  width: 100%;
  display: table;
  table-layout: fixed;
}

.assign-rack-table tbody tr:nth-child(odd) {
  background-color: #f2f2f2;
}

.assign-rack-table tbody tr td {
  padding: 8px 12px;
  font-family: '<PERSON>o Sans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  color: #000;
}

.assign-rack-table tbody tr td:nth-child(2) span,
.assign-rack-table tbody tr td:nth-child(4) span {
  cursor: pointer;
}

.assign-rack-table tbody tr td:nth-child(1) span,
.assign-rack-table tbody tr td:nth-child(3) span,
.assign-rack-table tbody tr td:nth-child(5) span {
  cursor: default !important;
}

.assign-rack-table tbody tr td:nth-child(3) {
  padding: 8px 3px;
}

.assign-rack-table th,
.assign-rack-table td {
  overflow: hidden;
  text-overflow: ellipsis;
}

.assign-rack-table th:nth-child(1) {
  width: 20% !important;
  padding-left: 30px !important;
}

.assign-rack-table th:nth-child(2) {
  width: 19% !important;
}

.assign-rack-table th:nth-child(3) {
  width: 21%;
}

.assign-rack-table th:nth-child(4) {
  width: 19% !important;
}

.assign-rack-table-content {
  flex: 1;
  overflow-y: auto;
  display: block;
  scrollbar-gutter: stable;
}

.assign-rack-wrapper {
  height: calc(100vh - 250px);
}

.border-custom-danger {
  border-radius: 6px;
  border: 2px solid var(--Error-Element---FD3A3A, #fd3a3a) !important;
  outline: #fd3a3a;
}

.assign-rack-table tbody tr td:nth-child(4) {
  padding-left: 25px;
  max-width: 10ch;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  color: #2174d4;
  font-weight: 500;
}

.assign-rack-wrapper .assign-rack-table-header .poppins-14-normal-500 {
  color: #303030 !important;
}

.assign-ticket-rack-input {
  border: 1px solid #eaeaea !important;
}

.assign-ticket-rack-input:focus {
  outline: 2px solid #00c6b7 !important;
}

.assign-rack-table-content .poppins-16-normal-400:nth-child(2),
.poppins-16-normal-400:nth-child(4) span {
  color: #2174d4;
  text-decoration: underline;
}

.assign-rack-wrapper th {
  font-size: 16px !important;
  padding-top: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.assign-rack-wrapper th:last-child {
  padding-left: -10px !important;
}

.assign-rack-wrapper .infinite-scroll-component__outerdiv {
  margin-right: 20px;
}

.already-assign-modal {
  width: 100%;
  max-width: 431px !important;
  border-radius: 10px;
}

.highlight-left-border {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.highlight-left-border::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 10px;
  background-color: #00d0b6;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.assign-rack-wrapper .row-of-table {
  margin-bottom: 8px;
  border-radius: 8px;
  box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.2);
  padding-top: 15px;
  padding-bottom: 15px;
  height: 42px;
}

.assign-rack-wrapper .row-of-table td:nth-child(1) {
  padding-left: 30px !important;
}
