export const productComponents = {
  ticketsTable: [1, 2, 3],
  ticketsTableWithCustomerData: [2, 3],
  headerSearchBar: [2, 3],
  p1: [1],
  p2: [2],
  p3: [3],
  cleaningType: [3],
  cleaningWeightType: [2, 3],
  maidType: [2, 3],
  storePickupDate: [2, 3],
  storageFulfillment: [3],
  rackManagement: [3],
  loadManagementDryClean: [3],
  loadManagementLaundry: [3],
  loadManagementCleaningWeight: [2, 3],
  workLoadTrackerDryClean: [3],
  workLoadTrackerLaundry: [3],
  workLoadTrackerCleaningWeight: [2, 3],
  allowedPaymentOptions: [2, 3],
  ticketSettingPrintCopies: [1, 2, 3],
  ticketSettingStoreInformation: [1, 2, 3],
  ticketSettingBarcode: [1, 2, 3],
  ticketSettingAddOnPrice: [1, 2, 3],
  ticketSettingCustomerPhoneNumber: [2, 3],
  ticketSettingCustomerAddress: [2, 3],
  ticketSettingRush: [2, 3],
  ticketSettingMessageOn: [2, 3],
  ticketSettingDryClean: [2, 3],
  ticketSettingWetClean: [2, 3],
  ticketSettingCleaningWeight: [2, 3],
  ticketSettingAlteration: [2, 3],
  rackManagementSetting: [3],
  salesAnalysisCleaningItemType: [3],
  salesAnalysisCleaningWeightItemType: [2, 3],
  salesAnalysisSalesItemType: [2, 3],
  salesAnalysisMaidItemType: [2, 3],
  salesAnalysisOthersItemType: [2, 3],
} as const

export type Component = keyof typeof productComponents

export enum ComponentAccess {
  ticketsTable = 'ticketsTable',
  ticketsTableWithCustomerData = 'ticketsTableWithCustomerData',
  headerSearchBar = 'headerSearchBar',
  cleaningType = 'cleaningType',
  p1 = 'p1',
  p2 = 'p2',
  p3 = 'p3',
  cleaningWeightType = 'cleaningWeightType',
  maidType = 'maidType',
  storePickupDate = 'storePickupDate',
  storageFulfillment = 'storageFulfillment',
  rackManagement = 'rackManagement',
  loadManagementDryClean = 'loadManagementDryClean',
  loadManagementLaundry = 'loadManagementLaundry',
  loadManagementCleaningWeight = 'loadManagementCleaningWeight',
  workLoadTrackerDryClean = 'workLoadTrackerDryClean',
  workLoadTrackerLaundry = 'workLoadTrackerLaundry',
  workLoadTrackerCleaningWeight = 'workLoadTrackerCleaningWeight',
  allowedPaymentOptions = 'allowedPaymentOptions',
  ticketSettingPrintCopies = 'ticketSettingPrintCopies',
  ticketSettingStoreInformation = 'ticketSettingStoreInformation',
  ticketSettingBarcode = 'ticketSettingBarcode',
  ticketSettingAddOnPrice = 'ticketSettingAddOnPrice',
  ticketSettingCustomerPhoneNumber = 'ticketSettingCustomerPhoneNumber',
  ticketSettingCustomerAddress = 'ticketSettingCustomerAddress',
  ticketSettingRush = 'ticketSettingRush',
  ticketSettingMessageOn = 'ticketSettingMessageOn',
  ticketSettingDryClean = 'ticketSettingDryClean',
  ticketSettingWetClean = 'ticketSettingWetClean',
  ticketSettingCleaningWeight = 'ticketSettingCleaningWeight',
  ticketSettingAlteration = 'ticketSettingAlteration',
  rackManagementSetting = 'rackManagementSetting',
  salesAnalysisCleaningItemType = 'salesAnalysisCleaningItemType',
  salesAnalysisCleaningWeightItemType = 'salesAnalysisCleaningWeightItemType',
  salesAnalysisSalesItemType = 'salesAnalysisSalesItemType',
  salesAnalysisMaidItemType = 'salesAnalysisMaidItemType',
  salesAnalysisOthersItemType = 'salesAnalysisOthersItemType',
}
