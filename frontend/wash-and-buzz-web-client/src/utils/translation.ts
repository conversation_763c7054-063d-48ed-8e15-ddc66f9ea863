export interface translation_Props {
  [key: string]: string
}

export interface translation_Props_With_Function {
  [key: string]: (
    value: string | number,
    ...rest: (string | number)[]
  ) => string
}

export const translation: translation_Props = {
  PERCENTAGE_SYMBOL: '%',
  HASH_SYMBOL: '#',
  CHANGE_USER: 'Change User',
  CHANGE_STORE: 'Change Store',
  CONFIRM_LOGOUT: 'Confirm Logout',
  CONFIRM: 'Confirm',
  CUSTOMER_MENU: 'Customer Menu',
  STORE_DETAILS: 'Store Details',
  INDIVIDUAL: 'Individual',
  WHOLESALE: 'Wholesale',
  HOTEL: 'Hotel',
  MANUAL: 'Manual',
  STORE_HOURS: 'Store Hours',
  ITEM_AND_PRICE_LIST: 'Items & Price Lists',
  ITEM_AND_PRICE_LIST_BEFORE: 'No item created in POS Go to',
  ITEM_AND_PRICE_LIST_AFTER: 'to create item',
  ADD_NEW_CUSTOMER_PROFILE: 'Add New Customer Profile',
  NEW_CUSTOMER_PROFILE: 'New Customer Profile',
  ITEM_AND_PRICE_LIST_DESCRIPTION:
    'Create and price your services: Add new items like shirts, dresses, or comforters, and set their base prices here.',
  HELP_MODAL_ITEM_AND_PRICE_LIST_DESCRIPTION:
    'Here, add all your unique items, defining their types (sales, dry cleaning, etc.) and setting their base prices.',
  HELP_MODAL_CATEGORY_DESCRIPTION:
    'Create categories to group your items logically. Assign each item to a relevant category for easy browsing and management.',
  HELP_MODAL_VARIANT_DESCRIPTION:
    'Create product variations (sizes, colors, etc.) and define add-on options. Link them to specific items and set custom prices for each item and add-on combination.',
  HELP_MODAL_ITEM_AND_PRICE_LIST_TITLE: 'Step 1: Set the Foundation',
  HELP_MODAL_CATEGORY_TITLE: 'Step 2: Organize by Category',
  HELP_MODAL_VARIANT_TITLE: 'Step 3: Add Variations and Options',
  ITEM: 'Items',
  PRICE_LISTS: 'Price Lists',
  CREATE_ITEM: 'Create Item',
  HELP_ONBOARDING_PROCESS: 'Help - Onboarding Process',
  SELECT_ITEM_CATEGORIES_TXT: 'Select items to assign to category',
  CREATE_CATEGORY: 'Create Category',
  CREATE_PRICE_LIST: 'Create Price List',
  CATEGORY: 'Category',
  NO_CATEGORY_CREATED: 'No category created',
  NO_CATEGORY_SELECTED: 'No category selected',
  CLICK_STAR_DESCRIPTION:
    'Click the star to "favorite" frequently used items, keeping them conveniently at the top of the category for easy access.',
  NO_ITEM_ASSIGNED_TO_CATEGORY: 'No item assigned to category',
  VARIANT: 'Variant',
  NO_VARIANT_SELECTED: 'No variant selected',
  ADD_ONS: 'Add-ons',
  ADD_ON: 'Add-On',
  ADD_ONS_DESCRIPTION:
    'Add-ons are the specific options under a variant that appear after selecting an item on your POS',
  EDIT_ADD_ONS: 'Edit add-ons',
  POS_SELECTION_RULE: 'POS Selection Rules',
  IS_VARIANT_REQUIRED: 'Is this variant required or optional?',
  REQUIRED: 'Required',
  HOW_MANY_SELECTED_VARIANT:
    'How many add-ons can be selected in this variant?',
  EMPTY_ADD_ONS_TEXT: 'No add-ons assigned to variant',
  APPLIED_ITEMS: 'Applied items',
  EDIT_APPLIED_ITEMS: 'Edit applied items',
  EMPTY_APPLIED_ITEMS_TEXT: 'No items applied to variant',
  NO_VARIANT_CREATED: 'No variant created',
  CREATE_VARIANT: 'Create variant',
  REARRANGE_CATEGORY: 'Rearrange Category',
  REARRANGE_CATEGORY_DESCRIPTION:
    'Rearrange the categories below to order them according to your preference. This will change how they appear on your POS screen, making it easier to find the items you need faster.',
  DISPLAY_ORDER: 'Display Order',
  CATEGORY_NAME: 'Name',
  PRICE_ADJUSTMENT: 'Price Adjustment',
  PRICE_ADJUSTMENTS: 'Price adjustment(s)',
  PRICE_ADJUSTMENT_ERROR:
    'Invalid payment incentive amount, must be greater than 0 and less than 1.',
  CREATE_DISCOUNTS_AND_SURCHARGES: 'Create discounts and/or surcharges.',
  PAYMENT_INCENTIVES_DESCRIPTION:
    'Select one payment method incentive to offer during checkout. This chosen incentive will be automatically applied to customer totals based on their selected payment method.',
  NONE: 'None',
  CREDIT_CARD_SURCHARGE: 'Credit Card Surcharge',
  CASH_DISCOUNT: 'Cash Discount',
  NAME: 'Name',
  ENTER_NAME: 'Enter Name',
  ADJUSTMENT_TYPE: 'Adjustment Type',
  SELECT_ADJUSTMENT_TYPE: 'Select Adjustment Type',
  AMOUNT: 'Amount',
  REQUIRED_AMOUNT: 'Amount*',
  ENTER_AMOUNT: 'Enter Amount',
  CANCEL: 'Cancel',
  SAVE: 'Save',
  UPCHARGE_TABLE: 'Upcharge Table',
  EDIT_TABLE_TEXT: 'Edit table',
  HIDE_CATEGORY: 'Hide Category',
  HIDE: 'Hide',
  RESTORE_CATEGORY: 'Restore Category',
  DELETE: 'Delete',
  DELETE_PRICE_ADJUSTMENT_DESCRIPTION:
    'Would you like to delete price adjustment?',
  DELETE_CONFIRMATION: 'Delete Confirmation',
  DELETE_PRICE_ADJUSTMENT: 'Delete Price Adjustment',
  TAX_PAGE_TITLE: 'Tax',
  TAX_PAGE_DESCRIPTION:
    'Configure your POS system with specific tax rates for the following options.',
  WEIGHTED: 'Weighted',
  CLEANING: 'Cleaning',
  MAID: 'Maid',
  OTHER_TAX: 'Other Tax',
  SELECT_DISCOUNT_METHOD:
    'Select the method that best suits your business needs for applying discounts',
  PRE_TAX: 'Pre-Tax Discount (Discounts Before Tax Calculation)',
  POST_TAX: 'Post-Tax Discount (Discounts After Tax Calculation)',
  DELETE_VARIANT: 'Delete Variant',
  DELETE_ITEM: 'Delete Item',
  MANAGE_ADD_ONS: 'Manage Add-on(s)',
  ITEMS: 'Items',
  SELECT_ITEM_VARIANT_TXT: 'Select items to apply to variant',
  MANAGE_VARIANT: 'Manage Variants',
  UPCHARGE_TABLE_NOTE:
    'Note: Type in "-" (dash) to not display the add-ons on the POS for that item.',
  MANAGE_VARIANT_DESCRIPTION:
    'Rearrange variants below to order them exactly how you want them to appear on your POS. ',
  CREATE_NEW_ADD_ON: 'Create New Add-on',
  PAYMENT_OPTION: 'Payment Options',
  TOTAL_AMOUNT: 'Total Amount',
  CASH: 'Cash',
  CREDIT_CARD: 'Credit Card',
  CHECK: 'Check',
  MULTIPAY: 'Multipay',
  TAX_TYPE_OTHER: 'Other',
  APPLY_ITEMS_TO: 'Apply Items to',
  DELETE_VARIANT_DESCRIPTION: 'Would you like to delete the variant forever?',
  REMOVE_TICKET_DESCRIPTION:
    'Are you sure you want to remove this ticket? Once removed, this action cannot be undone.',
  SALES: 'Sales',
  DISCOUNTS_AND_SURCHARGES: 'Discounts & Surcharges',
  TICKET: 'Ticket',
  ADD_ITEM_TO_START: 'Add item to start',
  SUBTOTAL: 'Subtotal',
  TAX: 'Tax',
  CHARGE: 'Charge',
  GENERAL_SETTINGS: 'General Settings',
  PRINT_STORE_COPIES: 'Print Store Copies',
  PRINT_COPIES_DESCRIPTION:
    'Enter the number of copies to print when a ticket is created.',
  TICKET_LAYOUT: 'Ticket Layout',
  TICKET_LAYOUT_DESCRIPTION:
    'Click on the toggle switch next to each element to display on the printed ticket',
  STORE_INFORMATION: 'Store Information',
  TICKET_BARCODE: 'Ticket Barcode',
  ITEM_ADD_ON_PRICES: 'Item Add-On Prices',
  ADD_ON_NAMED: 'An add-on named',
  ALREADY_ON_NAMED: 'already exists',
  EMPTY_UPCHARGE_TABLE_TEXT:
    'Create add-ons and apply items to edit the upcharges',
  TOTAL: 'Total',
  ENTER_CATEGORY: 'Enter Category',
  ENTER_PRICE_LIST: 'Enter Price List',
  ENTER_PIECES: 'Enter Pieces',
  PIECES: 'piece(s)',
  BAGS: 'bag(s)',
  BAG: 'Bag',
  ITEM_FLEXIBLE: 'item(s)',
  TODAY: 'Today',
  DATE_AND_TIME: 'Date & Time',
  DATE: 'Date',
  PRICE: 'Price',
  CLOSE: 'Close',
  REFUND: 'Refund',
  PRINT: 'Print',
  SORT_ASC: 'Sort ascending',
  SORT_DESC: 'Sort descending',
  CLEAR_SORT: 'Clear sort',
  CHANGE: 'Change',
  OUT_OF: 'Out of',
  PRINT_RECEIPT: 'Print Receipt',
  PRINT_STORE_COPY: 'Print Store Copy',
  APPLY: 'Apply',
  SELECT_DATE_RANGE: 'Select Date Range',
  YESTERDAY: 'Yesterday',
  YES: 'Yes',
  NO: 'No',
  THIS_WEEK: 'This Week',
  THIS_MONTH: 'This Month',
  THIS_YEAR: 'This Year',
  CUSTOM: 'Custom',
  LAST_WEEK: 'Last Week',
  TOMORROW: 'Tomorrow',
  NEXT_WEEK: 'Next Week',
  NEXT: 'Next',
  CASH_AMOUNT: 'Cash Amount',
  COMPLETE_PAYMENT: 'Complete Payment',
  FROM: 'From',
  TO: 'To',
  SUBLINE: 'Subline',
  INVALID_FROM_DATE: 'Invalid "From" Date',
  INVALID_TO_DATE: 'Invalid "To" Date',
  OPEN: 'Open',
  DUE: 'Due',
  ADD_CHECK_INFORMATION: 'Add Check Information',
  CHECK_AMOUNT: 'Check Amount*',
  CHECK_NUMBER: 'Check Number*',
  ADD_MULTIPAY_INFORMATION: 'Add Multipay Information',
  CREDIT_CARD_INSTRUCTION: 'Swipe, insert, or tap to pay',
  MANUAL_ENTRY: 'Manual Entry',
  ADD_CREDIT_CARD_INFORMATION: 'Add Credit Card Information',
  AMOUNT_CANNOT_BE_ZERO: 'Amount cannot be zero',
  AMOUNT_REQUIRED: 'Amount is required',
  COMP: 'Comp',
  CREDIT_CARD_NUMBER: 'Credit Card Number*',
  MONTH_YEAR: 'MM/YY*',
  CVV: 'CVV*',
  NAME_OF_CARD: 'Name on Card*',
  PAYMENT_AMOUNT: 'Payment Amount*',
  PAYMENT_AMOUNT_REQUIRED: 'Payment amount is required',
  MAKE_REQUIRED_CHOICES: 'Make required choices',
  SAVE_EDITS: 'Save Edits',
  CUSTOM_DISCOUNT: 'Custom Discount',
  DEBIT_MEMO: 'Debit Memo',
  CREDIT_MEMO: 'Credit Memo',
  EMPTY_TICKETS: 'No tickets available',
  VIEW_MORE: 'View More',
  VALUE_MUST_BE_BETWEEN_0_AND_100: 'Value must be between 0 and 100',
  DISCOUNT: 'Discount',
  SURCHARGE: 'Surcharge',
  MEMOS: 'Memos',
  CREDIT: 'Credit',
  DEBIT: 'DEBIT',
  NO_TICKETS_SELECTED: 'No tickets selected',
  EMPTY_VARIANT_ITEM:
    'No variants are associated with this item. Go to Item Library to apply variants to this item.',
  ITEM_DETAILS: 'Item Details',
  EDIT_STORE_DETAILS: 'Edit Store Details',
  EDIT_CATEGORY: 'Edit category',
  EDIT_DETAILS: 'Edit Details',
  PRICE_LIST: 'Price List',
  BASE_PRICE: 'Base Price',
  MIN_LBS: 'MinLbs',
  PRICE_PER_LB: 'PricePerLb',
  PRICE_PER_LB_REQUIRED: 'Price per lb required',
  UP_TO_LBS: 'Up to __ lbs',
  ADDITIONAL_PER_LB: 'Additional_per lb',
  NO_CATEGORY_AVAILABLE: 'No category available',
  VARIANTS: 'Variant (s)',
  EDIT_VARIANT: 'Edit variant',
  ADD_VARIANT: 'Add Variants',
  NO_VARIANT_AVAILABLE: 'No variant available',
  NO_UPCHARGE_AVAILABLE: 'No upcharge available',
  DONE: 'Done',
  LOGOUT_DESC: 'Would you like to logout?',
  LOGOUT: 'Logout',
  APPLY_CATEGORIES_TO_ITEM: 'Apply Categories to Item',
  SELECT_CATEGORIES_TO_APPLY_TO_ITEM: 'Select categories to apply to item',
  DELETE_ITEM_DESCRIPTION: 'Would you like to delete the item forever?',
  MANAGER_PRINTERS: 'Manage Printers',
  PRINTERS: 'Printers',
  USE_THIS_PRINTER_FOR: 'Use this printer for',
  PROVIDE_TO_CUSTOMER: 'Provide to customer',
  SHOW_HIDDEN: 'Show Hidden',
  SHOW_LESS: 'Show Less',
  ITEM_NAME: 'Item Name',
  ITEM_TYPE: 'Item Type',
  ENTER_PRICE: 'Enter Price',
  ENTER_LBS: 'Enter lbs',
  LBS: 'lbs',
  UNIT_PRICE: 'Unit Price',
  DESCRIPTION: 'Description',
  PRINTER_NAME: 'Printer Name*',
  ENTER_PRINTER_NAME: 'Enter Printer Name',
  MAC_ADDRESS: 'MAC Address*',
  ENTER_MAC_ADDRESS: 'Enter MAC Address',
  PASSWORD: 'Password*',
  ENTER_PASSWORD: 'Enter Password',
  USERNAME: 'Username*',
  ENTER_USERNAME: 'Enter Username',
  TICKETS_RECEIPTS: 'Tickets & Receipts',
  ADD_PRINTER: 'Add Printer',
  EDIT_PRINTER: 'Edit Printer',
  PRINTER_NAME_REQUIRED: 'Printer Name is required',
  MAC_ADDRESS_REQUIRED: 'MAC Address is required',
  USERNAME_REQUIRED: 'Username is required',
  PASSWORD_REQUIRED: 'Password is required',
  APPLY_VARIANTS_TO: 'Apply Variants to',
  APPLIED_VARIANTS: 'Applied Variants',
  SELECT_VARIANT_ITEM_TXT: 'Select variants to apply to item',
  SELECT_USER: 'Select User',
  STATUS: 'Status',
  DELETE_PRINTER_CONFIRMATION: 'Delete Printer Confirmation',
  DELETE_PRINTER_DESCRIPTION: 'Are you sure you want to delete this printer?',
  QTY: 'Qty',
  GUEST: 'Guest',
  PRINT_JOB: 'print-job',
  PUBLISH: 'Publish',
  REQUEST_CLIENT_STATUS: 'request-client-status',
  CLIENT_STATUS: 'client-status',
  EDIT: 'Edit',
  REFRESH: 'Refresh',
  ENABLE: 'Enable',
  DISABLE: 'Disable',
  DISABLED: 'Disabled',
  REFUND_AMOUNT: 'Refund Amount',
  REFUND_TICKET: 'Refund Ticket',
  RECEIPT_PAID_BY: 'Receipt Paid By',
  REFUND_TYPE: 'Refund Type',
  REASONS_FOR_REFUNDING_TICKET: 'Reasons for Refunding Ticket',
  DETAILS: 'Details',
  CUSTOMER_DISSATISFACTION: 'Customer Dissatisfaction',
  INTERNAL_ERROR: 'Internal Error',
  OTHER: 'Other',
  RECEIPT_PAID_BY_TEMPLATE: 'Credit Card Ending in 1234',
  SELECT_REASON: 'Select Reason',
  STAR_CLOUDPRNT: 'star/cloudprnt',
  TO_DEVICE: 'to-device',
  TO_SERVER: 'to-server',
  NO_ITEM_SELECTED: 'No item selected',
  CUSTOMER_TYPE: 'Customer Type*',
  FIRST_NAME: 'First Name*',
  LAST_NAME_REQUIRED_LABEL: 'Last Name*',
  LAST_NAME: 'Last Name',
  ADDRESS: 'Address*',
  UNIT: 'Unit',
  CITY: 'City*',
  STATE: 'State*',
  ZIPCODE: 'Zipcode*',
  ZIPCODE_PLACEHOLDER: 'Zipcode',
  STATE_PLACEHOLDER: 'State',
  CUSTOMER_TYPE_PLACEHOLDER: 'Customer Type',
  PRIMARY_PHONE_TYPE: 'Primary Phone Type*',
  PRIMARY_PHONE_NUMBER: 'Primary Phone Number*',
  SECONDARY_PHONE_TYPE: 'Secondary Phone Type',
  SECONDARY_PHONE_NUMBER: 'Secondary Phone Number',
  MOBILE: 'MOBILE',
  EMAIL: 'Email Address',
  MEMO: 'Memo',
  ENTER_FIRST_NAME: 'Enter first name',
  ENTER_LAST_NAME: 'Enter last name',
  ENTER_ADDRESS: 'Enter address',
  ENTER_CITY: 'Enter city',
  ENTER_UNIT: 'Enter unit',
  SELECT_PHONE_TYPE: 'Select Phone Type',
  ENTER_PHONE_NUMBER: 'Enter phone number',
  ENTER_EMAIL: 'Enter email address',
  ENTER_MEMO: 'Enter memo',
  PENDING: 'Pending',
  CONNECTED: 'Connected',
  CONNECT: 'Connect',
  NOT_CONNECTED: 'Not Connected',
  COMP_TICKET: 'Comp Ticket',
  DEVICE_NOT_FOUND: 'Device Not Found',
  ENABLED_DEVICES: 'Enabled Devices',
  DISABLED_DEVICES: 'Disabled Devices',
  POS: 'POS',
  TICKETS: 'Tickets',
  ASSIGN_RACK: 'Assign Rack',
  DELIVERY: 'Delivery',
  AR_BILLING: 'A/R Billing',
  OPERATIONS: 'Operations',
  REPORTS: 'Reports',
  MY_TEAM: 'My Team',
  ITEM_LIBRARY: 'Item Library',
  HARDWARE: 'Hardware',
  TICKET_SETTINGS: 'Ticket Settings',
  PRINT_TICKET: 'Print Ticket',
  PAYMENT_TERMINAL: 'Payment Terminal',
  VARIANTS_TITLE: 'Variants',
  CATEGORIES: 'Categories',
  OPTION_1: 'Option 1',
  OPTION_2: 'Option 2',
  REPORT_1: 'Report 1',
  REPORT_2: 'Report 2',
  TEAM_MEMBER_1: 'Team Member 1',
  TEAM_MEMBER_2: 'Team Member 2',
  MANAGE_PERMISSIONS: 'Manage Permissions',
  MANAGE_POSITIONS: 'Manage Positions',
  PERMISSIONS_DESCRIPTION:
    "Grant permissions that align with the typical tasks of this position without entering a users' pin.",
  POSITIONS_DESCRIPTION:
    'Rearrange the job roles below to arrange them in the order you want them to appear on your screen',
  ITEMS_PRICE_LIST: 'items-price-list',
  HOME: 'Home',
  INVALID_MAC_ADDRESS: 'Invalid MAC Address',
  VALID_PRINTER_NAME: 'Please enter a valid name',
  VALID_USERNAME: 'Please enter a valid username',
  SALES_SUMMARY_OVERVIEW: 'Sales Summary Overview',
  SALES_SUMMARY_OVERVIEW_DESC:
    'Please note that the following sales summary report is prepared using cash accounting principles.',
  TICKETS_SUMMARY_OVERVIEW: 'Tickets Summary Overview',
  SALES_BY_ITEM_TYPES: 'Sales by Item Types',
  SALES_BY_PAYMENT_TYPES: 'Sales by Payment Types',
  SALES_BY_ORDER_METHODS: 'Sales by Order Methods',
  SALES_REPORTS: 'Sales Reports',
  VIEW_SALES_SUMMARY_DETAILS: 'View Sales Summary Details',
  ITEM_TRANSACTION_REPORTS: 'Item Transaction Reports',
  TEAM_MEMBERS: 'Team Members',
  TEAM_MEMBER: 'Team Member',
  POSITIONS: 'Positions',
  RENAME: 'Rename',
  PERMISSIONS: 'Permissions',
  DUPLICATE: 'Duplicate',
  ENTER_POSITION: 'Enter Position',
  ADD_POSITION: 'Add Position',
  DELETE_POSITION_DESCRIPTION:
    'Are you sure you want to delete this position? Once deleting this position, it cannot be retrieved.',
  NAME_REQUIRED: 'Name is required',
  USER_PIN: 'User Pin',
  PIN_REQUIRED: 'User Pin is required',
  PIN_SHOULD_ONLY_CONTAIN_NUMBERS: 'PIN should only contain numbers.',
  NO_ENABLED_PRINTER_AVAILABLE: 'No enabled printers available',
  NO_DISABLED_PRINTER_AVAILABLE: 'No disabled printers available',
  CUSTOMER_NAME: 'Customer Name',
  STORE_DROP_OFF: 'Store Drop Off ·',
  BALANCE: 'Balance',
  STORE_AUTHORIZATION: 'store/authorizations',
  EMAIL_ADDRESS_NOT_RECOGNIZED: 'email address was not recognized.',
  ADD_TEAM_MEMBER: 'Add Team Member',
  MAIN_PAGE: 'Main Page',
  PROFILE: 'Profile',
  ADD_TERMINAL: 'Sync Terminals',
  NO_ENABLED_DEVICES_AVAILABLE: 'No enabled devices available',
  DEVICE_NAME: 'Device Name',
  ENTER_DEVICE_NAME: 'Enter Name',
  EDIT_DEVICE: 'Edit Device',
  ADD_DEVICE: 'Add Device',
  DEVICE_NAME_REQUIRED: 'Device name is required',
  VALID_DEVICE_NAME: 'Please enter a valid device name',
  HSN_NUMBER: 'HSN Number',
  POS_PRICE_ADJUSTMENT: 'POS_PRICE_ADJUSTMENT',
  PRICE_ADJUSTMENT_IN_POS: 'Price Adjustment in POS',
  DISCONNECT: 'Disconnect',
  NO_AVAILABLE_TERMINAL_DEVICES: `This store is not configured for integration with a payment
            processor. Please reach out to technical support if you would like
            to become an integrated partner and begin accepting card-present
            payments through the point-of-sale.`,
  WALLET: 'Wallet',
  PROFILE_TAB: 'profile',
  WALLET_TAB: 'wallet',
  DUPLICATE_PRINTER: 'Duplicate printer',
  DUPLICATE_PRINTER_DESCRIPTION:
    'A printer with this MAC Address could not be added. Please check the value on your device and try again, or contact technical support.',
  SWIPE_INSERT_OR_TAP_TO_PAY: 'Swipe, insert, or tap to pay',
  DEVICE_NAME_CARD_PAYMENT_TEMPLATE: 'ABC Cleaner Left Terminal',
  DEVICE: 'Device',
  DRAWER_ICON: 'drawer icon',
  ICON: 'icon',
  TEAM_MEMBER_ENTER_FIELD_DESCRIPTION: 'Enter member’s name and unique pin:',
  NAME_REQUIRED_LABEL: 'Name*',
  PIN_REQUIRED_LABEL: 'Pin*',
  ENTER_PIN: 'Enter pin',
  POSITION_REQUIRED: 'Please select any one position',
  ASSIGN_POSITION_DESCRIPTION: 'Assign a position to the member*',
  NO_TEAM_MEMBER_AVAILABLE: 'No team member available',
  REPORT_SETTINGS: 'Report Settings',
  REPORT_SETTINGS_DESC:
    'Choose your preferred accounting method for managing your business transactions. ',
  CASH_BASED_SALES_REPORTS: 'Cash Based Sales Reports',
  ACCRUAL_BASED_SALES_REPORTS: 'Accrual Based Sales Reports',
  ACCRUAL: 'Accrual',
  ONE_WEEK: '1 Week',
  ONE_MONTH: '1 Month',
  THREE_MONTHS: '3 Months',
  ALL: 'All',
  CLOSED: 'Closed',
  PAID: 'Paid',
  UNPAID: 'Unpaid',
  PARTIAL: 'Partial',
  FLAG: 'Flag',
  VOID: 'Void',
  UNCLAIMED: 'Unclaimed',
  FILTER_TYPE: 'Filter Type',
  PICKUP: 'Pickup',
  RECURRING: 'Recurring',
  OTHERS: 'Others',
  CREDIT_DEBIT_CARD: 'Credit/Debit Card',
  GROSS_SALES: 'Gross Sales',
  NET_SALES: 'Net Sales',
  TIP: 'Tip',
  ISSUED_TICKETS: 'Issued Tickets',
  PAID_ISSUED: 'Paid-Issued',
  UNPAID_ISSUED: 'Unpaid-Issued',
  COPY: '(Copy)',
  ADD_NOTE: 'Add Note',
  DELETE_TEAM_MEMBER_DESCRIPTION:
    'Are you sure you want to delete this member? Once deleting this member, it cannot be retrieved.',
  GENERAL_DISPLAY: 'General Display',
  PICKUP_DELIVERY: 'Pickup & Delivery',
  FULFILLMENT: 'Fulfillment',
  STORE_PICKUP: 'Store Pickup',
  GENERAL_DISPLAY_PAGE_TITLE: 'General Display',
  POS_SCREEN_LOCK: 'POS Screen Lock',
  ENABLE_POS_SCREEN_LOCK: 'Enable POS Screen Lock',
  POS_LAYOUT: 'POS Layout',
  GENERAL_DISPLAY_POS_LAYOUT_DESCRIPTION:
    'Select your preferred POS and Ticket Box display.',
  VARIANT_TICKET_SUMMARY: 'Variant/Ticket Summary',
  CONTINUATION_TOKEN: 'continuationToken',
  SEARCH: 'Search',
  SEARCH_TIMEZONE: 'Search Timezone',
  SELECT_STORE: 'Select Store',
  BACK: 'Back',
  DELETE_POSITION_ERROR:
    'This position has active assignments. Please remove all team members from this position before deleting.',
  DELETE_ERROR: 'Delete Error',
  IDLE_TIMEOUT: 'Idle Timeout',
  MINUTES: 'min(s)',
  ISSUED_TICKETS_ANALYTICS: 'Issued Tickets Analytics',
  TIME_GRANULARITY: 'Time Granularity',
  POTENTIAL_SALES: 'Potential Sales',
  CLICK_THE_TICKET_TO_PREVIEW: 'Click the ticket to preview',
  REVENUE_SUMMARY: 'Revenue Summary',
  REVENUE_SUMMARY_CASH_BASED: 'Revenue Summary (Cash-Based)',
  SALES_ANALYSIS: 'Sales Analysis',
  ITEM_TRANSACTIONS: 'Item Transactions',
  YTD: 'YTD',
  SELECT_PRINTERS: 'Select Printers',
  SELECT_PAYMENT_TERMINAL: 'Select Payment Terminal',
  NO_AVAILABLE_TERMINAL: 'No available terminals',
  SKIP: 'Skip',
  DISCOUNT_AND_COMPS: 'Discount & Comps',
  USER: 'User',
  SUBMIT: 'Submit',
  INVALID_USER_PIN: 'Invalid user pin',
  NO_AVAILABLE_PRINTERS: 'No available printers',
  USER_PREFERENCE: 'userPreference',
  COLLAPSE: 'Collapse',
  SHOW_MORE: 'Show More',
  SALES_GROSS: 'sales-gross',
  SALES_NET: 'sales-net',
  SALES_TOTAL: 'sales-total',
  TICKETS_ISSUED: 'tickets-issued',
  TICKETS_PAID: 'tickets-paid',
  TICKETS_UNPAID: 'tickets-unpaid',
  ITEMTYPE_SALES: 'itemtype-sales',
  ITEMTYPE_CLEANING: 'itemtype-cleaning',
  ITEMTYPE_CLEANING_WEIGHTED: 'itemtype-weighted',
  ITEMTYPE_MAID: 'itemtype-maid',
  ITEMTYPE_OTHER: 'itemtype-other',
  PAYMENT_CASH: 'payment-cash',
  PAYMENT_CREDITCARD: 'payment-credit-card',
  PAYMENT_CHECK: 'payment-check',
  PAYMENT_STORE_CREDIT: 'payment-store-credit',
  PAYMENT_OTHER: 'payment-other',
  CASH_BASED: 'Cash-Based',
  NUMBER_OF_TICKETS: '# of Tickets',
  NO_ANALYTICS_AVAILABLE: 'No analytics available',
  ONE_DAY: '1 Day',
  BARCODE_INPUT: 'Barcode Input',
  ENTER: 'Enter',
  ADD_SUBLINE: 'Add Subline',
  STORE_NAME_IS_REQUIRED: 'Store Name is required',
  ADDRESS_IS_REQUIRED: 'Address is required',
  CITY_IS_REQUIRED: 'City is required',
  STATE_IS_REQUIRED: 'State is required',
  PLEASE_SELECT_ANY: 'Please Select Any',
  ZIP_CODE_IS_REQUIRED: 'Zip Code is required',
  PHONE_NUMBER_IS_REQUIRED: 'Phone Number is required',
  INVALID_PHONE_NUMBER: 'Invalid number',
  CUSTOMER_TYPE_IS_REQUIRED: 'Customer type is required',
  FIRST_NAME_IS_REQUIRED: 'First name is required',
  LAST_NAME_IS_REQUIRED: 'Last name is required',
  ADDRESS_LINE_IS_REQUIRED: 'Address line is required',
  PRIMARY_PHONE_TYPE_IS_REQUIRED: 'Primary phone type is required',
  PRIMARY_PHONE_NUMBER_IS_REQUIRED: 'Primary phone number is required',
  CHECK_AMOUNT_MUST_BE: `Check amount must be`,
  CHECK_AMOUNT_IS_REQUIRED: 'Check amount is required',
  CHECK_NUMBER_IS_REQUIRED: 'Check number is required',
  ITEM_NAME_IS_REQUIRED: 'Item name is required',
  ITEM_TYPE_IS_REQUIRED: 'Item type is required',
  PRICE_LIST_ID_IS_REQUIRED: 'Price list ID is required',
  PRINTING_COMPLETE: 'Printing complete',
  PRINTING_INPROGRESS: 'Printing...',
  PRINTING_ERROR: 'Error. Please check printer configuration',
  DASHBOARD: 'Dashboard',
  INDEX_DB: 'IndexDb',
  SSE: 'Sse',
  THEME_UI: 'Themeui',
  WEBSOCKET: 'Websocket',
  DEFAULT_STATE: 'Default State',
  REDUX: 'Redux',
  MQTT: 'Mqtt',
  IFRAME_STYLING: 'Iframe Styling',
  BARCODE_SCANNER: 'Barcode Scanner',
  PAGES: 'Pages',
  STM_EDITOR: 'STM Editor',
  OPEN_EDITOR: 'Open Editor',
  CONNECTED_PRINTER_NAME: 'Connected Printer Name',
  REQUEST_TIMEOUT: 'Request Timeout',
  ORIGINAL_REQUEST_MISSING: 'Original request is missing',
  NEW_ACCESS_TOKEN_MISSING: 'New access token is missing',
  NO_ACCESS_TOKEN_AVAILABLE: 'No access token available',
  ORIGINAL_REQUEST_IS_MISSING: 'Original request is missing',
  ASSIGNED_ITEMS: 'Assigned Items',
  GENERAL_TAB: 'general',
  STORE_PICKUP_TAB: 'store-pickup',
  PICKUP_DELIVERY_TAB: 'pickup-delivery',
  GENERAL: 'General',
  RUSH: 'Rush',
  RUSH_DESC: 'Enable rush fee',
  RUSH_FEE: 'Rush fee',
  LOAD_MANAGEMENT: 'Load Management',
  RUSH_KEY: 'rush',
  LOAD_MANAGEMENT_KEY: 'loadManagement',
  LOAD_MANAGEMENT_DESC: 'Enable color-code for load management',
  STORE_PICKUP_PREFERENCES: 'Store Pickup Preferences',
  STORE_PICKUP_PREFERENCES_DESC:
    'Configure default pickup date and customize store pickup time(s).',
  RECOMMENDED_PICKUP_DATE: 'Recommended  Pickup Date for Cleaning Items',
  DAYS_AFTER_DROP_OFF_PICKUP: 'day(s) after drop-off/pick-up',
  SELECTABLE_STORE_PICKUP_TIME: 'Selectable Store Pickup Time(s)',
  SELECTABLE_STORE_PICKUP_TIME_DESC:
    'Customize the available time for store pickup times.',
  TURN_RED_IF_MORE_THAN: 'Turn red if more than',
  LOADS: 'load(s)',
  DRY_CLEANING: 'Dry cleaning',
  WET_CLEANING: 'Wet cleaning',
  CLEANING_WEIGHT: 'Cleaning (Weight)',
  ADD_TIME_SLOT: 'Add Time Slot',
  PICKUP_TIME: 'Pickup Time',
  PICKUP_TIME_REQUIRED: 'Pickup Time*',
  MM_PLACEHOLDER: 'MM',
  HH_PLACEHOLDER: 'HH',
  AM: 'AM',
  PM: 'PM',
  DELETE_TIME_SLOT: 'Delete Time Slot',
  STORAGE: 'Storage',
  ENABLE_MULTIPLE_TICKETS_PER_RACK: 'Enable multiple tickets per rack',
  ENABLE_STORAGE: 'Enable storage',
  WEIGHTED_TEXT: 'WEIGHTED',
  ADD_PRICE_LIST: 'Add Price List',
  SERVICE: 'Service',
  DOLLAR_SYMBOL: '$',
  AND_SYMBOL: '&',
  EQUAL_SYMBOL: '=',
  ALTERATIONS: 'Alterations',
  PRICING: 'Pricing',
  FABRIC_DETAILS: 'Fabric Details',
  COLOR_WHITE: 'COLOR_WHITE',
  WHITE: 'White',
  DEFAULT_SERVICE: 'Default Service',
  ITEM_PIECE: 'Item Piece(s)',
  CLIENT_CONNECTED: 'CLIENT_CONNECTED',
  ISVALIDCLIENT: 'isValidClient',
  ISSUBSCRIBE: 'isSubscribe',
  NO_AVAILABLE_TICKET_FOR_CHECKOUT: 'No available ticket for checkout',
  QUICK_CHECKOUT: 'Quick Checkout',
  QUICK_CHECKOUT_KEY: 'quickCheckout',
  SALES_KEY: 'sales',
  CLOSE_TIME_VALIDATION_MESSAGE: 'Close time must be after open time',
  OPEN_24_HOURS: 'Open 24 hours',
  INVALID_TIME_FORMAT: 'Invalid Time Format',
  ALL_FIELDS_ARE_REQUIRED: 'All Fields are required',
  OPEN_TIME: 'Open Time',
  CLOSE_TIME: 'Close Time',
  TWELVE_O_CLOCK: '12:00',
  TIME_HOUR_12: '12',
  TIME_MINUTES_00: '00',
  ITEM_PIECES_REQUIRED: 'Item Piece Required',
  CUSTOMER_FIELD_NAME: 'customerName',
  PAYMENT_MODAL_CUSTOMER_NAME_LABEL: 'Customer Name*',
  ZIPCODE_FIELD_NAME: 'zipcode',
  PAYMENT_AMOUNT_FIELD_NAME: 'paymentAmount',
  INVALID_ZIP_CODE: 'Invalid zip Code',
  CUSTOMER_NAME_FIELD_REQUIRED: 'Customer name is required',
  ZIP_CODE_FIELD_REQUIRED: 'Zipcode is required',
  FULFILLMENT_MODAL: 'Fulfillment Details',
  PREPAY: 'Prepay',
  FULFILLMENT_OPTION: 'Fulfillment Option',
  READY_BY: 'Ready by: ',
  PIECE: 'Piece',
  LOAD_PER_LBS: 'Load/lbs',
  LOAD: 'Load',
  ITEM_WEIGHT: 'Item Weight',
  RUSH_TODAY: 'Rush Today',
  SELECTED_DATE: 'Selected date',
  ADJUSTED_TOTAL: 'Adjusted Total',
  PAY: 'Pay',
  PAY_WITH: 'Pay With',
  ADD_TIP_OR_STORE_CREDIT: 'Add a tip or store credit',
  AMOUNT_TENDERED: 'Amount Tendered',
  ADD: 'Add',
  STORE_CREDIT: 'Store Credit',
  ADD_STORE_CREDIT: 'Add store credit to customer’s profile',
  PROVIDE_CASH_TO_CUSTOMER: 'Provide cash to customer',
  PAY_LATER: 'Pay later',
  CREATE_TICKET: 'Create Ticket',
  RECEIVE_CASH_FROM_CUSTOMER: 'Receive cash from customer',
  TICKET_CREATION_COMPLETED: 'Ticket Creation Completed',
  TICKET_MODIFICATION_COMPLETED: 'Updates have been made to ticket.',
  CHECK_AMOUNT_MUST_BE_GREATER_THAN_ZERO: 'Check amount must be greater than 0',
  CREDIT_CARD_AMOUNT_MUST_BE_GREATER_THAN_ZERO:
    'Credit Card amount must be greater than 0',
  PRINT_OPTION: 'Print Options',
  PRINT_SUB_TICKET: 'Print Sub-Ticket',
  PRINT_ALL: 'Print All',
  SUB_TICKETS: 'Sub-Tickets',
  MESSAGE_ON_TICKET: 'Message on Ticket',
  PAY_AND_CHECKOUT: 'Pay & Checkout',
  CHECKOUT: 'Checkout',
  CHECKOUT_TICKET: 'Checkout Ticket',
  TICKET_BALANCE: 'Ticket Balance',
  CHECKOUT_COMPLETED: 'Checkout Completed',
  NO_PRINTER_CONNECTED: 'No printer connected.',
  LAST_7_DAYS: 'Last 7 Days',
  LAST_30_DAYS: 'Last 30 Days',
  LAST_90_DAYS: 'Last 90 Days',
  LAST_12_MONTHS: 'Last 12 Months',
  LAST_7_DAYS_VALUE: 'last7Days',
  LAST_30_DAYS_VALUE: 'last30Days',
  LAST_90_DAYS_VALUE: 'last90Days',
  LAST_12_MONTHS_VALUE: 'last12Months',
  QUICK_CHECKOUT_FILTER: 'quick-checkout',
  REOPEN: 'Reopen',
  REVERSE: 'Reverse',
  REMOVE_INVENTORY: 'Remove Inventory',
  BALANCE_DUE: 'Balance Due',
  MORE: 'More',
  MORE_OPTIONS: 'More Options',
  REMOVE_TICKET_CONFIRMATION: 'Remove Ticket Confirmation',
  LEAVE_WITHOUT_SAVING_DESCRIPTION:
    'Are you sure you want to leave? Any unsaved changes will be discarded.',
  LEAVE_WITHOUT_SAVING: 'Leave Without Saving',
  UNSAVED_CHANGES: 'Unsaved Changes',
  REMOVE: 'Remove',
  REFUND_TICKET_CONFIRMATION_TEXT:
    'Are you sure you want to refund this ticket?',
  VOID_TICKET: 'Void Ticket',
  VOID_TICKET_REASON_TITLE: 'Reasons for Voiding Ticket',
  VOID_TICKET_CONFIRMATION: 'Void Ticket Confirmation',
  VOID_TICKET_DESCRIPTION:
    'Are you sure you want to void this ticket? Once voided, this action cannot be undone.',
  SEARCH_CUSTOMER: 'Search Customer',
  SEE_ALL_TICKETS: ' See All Tickets',
  POC_MAIN_PAGE: 'Poc Main Page',
  CUSTOM_FILTER: 'custom',
  SELECTION: 'selection',
  REFUND_AND_COMP: 'Refund & Comp',
  AR_BALANCE: 'A/R Balance',
  ACCOUNT_STATEMENT: 'Account Statement',
  GENERATE_STATEMENT: 'Generate Statement',
  ACCOUNT_INVOICE: 'Account Invoice',
  ACCOUNT_DESC: 'All unpaid tickets up to date',
  GENERATE_INVOICE: 'Generate Invoice',
  REOPEN_TICKET: 'Reopen Ticket',
  REOPEN_TICKET_DESCRIPTION: 'Are you sure you want to reopen this ticket?',
  DOWNLOAD: 'Download',
  SHARE: 'Share',
  ISSUED_BY: 'Issued By',
  STATEMENT_DATE: 'Statement Date',
  COVERED_PERIOD: 'Covered Period',
  OPENING_BALANCE: 'Opening Balance',
  DEBIT_CHARGE: 'Debit/Charge',
  CREDIT_RECEIPT: 'Credit/Receipt',
  ENDING_BALANCE: 'Ending Balance',
  NUMBER_OF_TRANSACTIONS: 'Number of Transactions',
  TYPE: 'Type',
  NOTES: 'Notes',
  RECEIPT: 'Receipt',
  INVOICE_DATE: 'Invoice Date',
  INVOICE: 'Invoice',
  BILL_TO: 'Bill To',
  OUTSTANDING_BALANCE: 'Outstanding Balance',
  NO_DATA_AVAILABLE: 'No Data Available',
  ADDITIONAL_NOTES: 'Additional Notes',
  ITEM_QUANTITY: 'Item Quantity',
  PAY_ONLY: 'Pay Only',
  RACK_LIST: 'Rack List',
  TICKET_IDENTIFIER: 'Ticket #',
  RACK_IDENTIFIER: 'Rack #',
  INVALID_TICKET: 'Invalid Ticket #',
  NO_ICON_SELECTED: 'No Icon Selected',
  TICKET_ALREADY_ASSIGNED: 'Ticket Already Assigned',
  MOVE_TO: 'Move to',
  WOULD_YOU_LIKE_TO_MOVE_IT: 'Would you like to move it?',
  IS_ALREADY_IN_RACK: 'is already in Rack',
  NO_RACK_ASSIGNMENT_AVAILABLE: 'No rack assignment available',
  TICKET_FILTERS: 'Ticket Filters',
  TICKET_STATUS: 'Ticket Status',
  PAYMENT_STATUS: 'Payment Status',
  DATE_RANGE: 'Date Range',
  CLEAR_FILTER: 'Clear Filter',
  FROM_DATE_VALIDATION: 'From date is required',
  TO_DATE_VALIDATION: 'To date is required',
  INVALID_DATE_FORMAT: 'Invalid format (MM/DD/YYYY)',
  TO_DATE_MUST_BE_AFTER_FROM_DATE: 'To date must be after From date',
  ICON_LIBRARY: 'Icon Library',
  NO_FUTURE_DATE: 'Future dates are not allowed',
  LAUNDRY: 'Laundry',
  RACK_MANAGEMENT: 'Rack Management',
  ENABLE_RACK_MANAGEMENT: 'Enable multiple tickets per rack',
  RACK_MANAGEMENT_NAME: 'allowMultiTicketRackAssignment',
  UNEXPECTED_ERROR_OCCURRED: 'Unexpected error occurred',
  CURRENTLY_IN_THIS_RACK: 'is currently in this rack',
  INVALID_TICKET_STATUS: 'Invalid ticket status for tickets:',
  MUST_BE_OPEN: '(must be OPEN)',
  ASSIGN: 'Assign',
  AMOUNT_MUST_BE_GREATER_THAN_ZERO: 'Amount must be greater than 0',
  PAYMENT_METHOD_IS_REQUIRED: 'Payment method is required',
  REQUIRED_PAYMENT_METHOD: 'Payment Method*',
  OTHER_PAYMENT_PLACEHOLDER: 'Ex. Zelle, Venmo, Cash App',
  CARD: 'Card',
}

export const transactionWithFunction: translation_Props_With_Function = {
  amountShouldBeInRange: (value, ...rest) => {
    const minimumAmount = value as string
    const maximumAmount = rest[0] as string
    return `Amount should be in the range of ${minimumAmount} to ${maximumAmount}`
  },
  applyCategoryToItem: (value) => {
    return `Apply Categories to ${value} Item`
  },
  additionalNotes: (value) => {
    return `Apply Categories to ${value} Item`
  },
  applyVariantToItem: (value) => {
    return `Make all checks payable to ${value}.`
  },
}

export const errorMessageWithFunction: translation_Props_With_Function = {
  anotherTicketCurrentlyInThisRack: (ticket: string | number) =>
    `Ticket ${ticket} is currently in this rack`,

  ticketAlreadyInAnotherRack: (
    ticket: string | number,
    rack: string | number
  ) => `Ticket ${ticket} is already in Rack ${rack}`,

  duplicateTicketRackAssignment: (
    ticket: string | number,
    rack: string | number
  ) => `Ticket ${ticket} is already assigned to ${rack}`,
}
