import { Customers } from '@/types/module/addCustomersModule'
import { TextSale } from '@/types/module/commonModule'
import { Fulfillment } from '@/types/module/fulfillmentModule'
import {
  SelectedPriceAdjustmentData,
  selectedItemData,
} from '@/types/module/itemModule'
import { PriceAdjustment } from '@/types/module/priceAdjustmentModule'
import { PrintOptions } from '@/types/module/printersModule'
import { Store } from '@/types/module/storeModule'
import { TeamMember } from '@/types/module/teamMembersModule'
import {
  PaymentStatusForFilter,
  TicketStatusForFilter,
} from '@/types/module/ticketFiltersModule'
import { TicketSettings } from '@/types/module/ticketSettingsModule'
import { Variant } from '@/types/module/variantsModule'
import { translation } from '@/utils/translation'
import { MutableRefObject } from 'react'
import { ThemeUIStyleObject } from 'theme-ui'
import { v4 as uuidv4 } from 'uuid'

export interface SalesProps {
  saleRef: React.RefObject<HTMLDivElement>
  clearEmptySublines: () => void
  onScrollToRequired: (index: number) => void
  isShowMore: boolean
  pathName: string
  executeIfEditable: (callBack: () => void, isPriceAdjustment?: boolean) => void
  isProgrammaticScroll: MutableRefObject<boolean>
  getQuickCheckoutData: (fetchMoreData?: boolean) => void
  quickCheckoutCalledRef: MutableRefObject<boolean>
}
export interface VariantBoxProps {
  onClick: () => void
  onClickCancel: () => void
  isShowMore: boolean
  variantRef: React.RefObject<HTMLDivElement>
  requiredVariantsRef: React.RefObject<HTMLDivElement>
  isProgrammaticScroll: MutableRefObject<boolean>
  executeIfEditable: (callback: () => void) => void
}

export interface SalesData {
  title: string
}

export interface TicketProps {
  ticket?: TicketData
  mode?: PosLayoutMode
  onRefund?: (ticket: TicketData) => void
  paidAmount?: number
  setPayableAmountData: () => void
}

export enum PosLayoutMode {
  POS = 'POS',
  LIST = 'LIST',
  QUICK_CHECKOUT = 'QUICK_CHECKOUT',
}

export enum RequestedAction {
  RESET_CUSTOMER_DATA = 'RESET_CUSTOMER_DATA',
}

export type SublineUpdate = 'ADD' | 'DELETE'

export interface CurrentTicketProps {
  onScrollToRequired: () => void
  saleRef: React.RefObject<HTMLDivElement>
  variantRef: React.RefObject<HTMLDivElement>
  clearEmptySublines: () => void
  onClickDelete: () => void
  handleClickSaveEdit: () => void
  scrollToBottom: (index: number) => void
  executeIfEditable: (callBack: () => void) => void
  actualTicketData?: TicketData
}

/* Interface representing any price modifier for the Item's base price. Used in variants and sub-lines.
 *
 * name to display in the Ticket box
 * priceChange positive or negative number with the corresponding amount to increase or decrease from the base price
 */
export interface ItemPriceModifier {
  name: string
  priceChange: number
}

/* Item representation with all fields required for the non-empty ticket box */
export interface Item {
  id: string
  quantity: number
  description: string
  unitPrice: number
  subtotal: number // quantity * unitPrice
  variants: ItemPriceModifier[]
  subLines: ItemPriceModifier[]
  tax: number // Dollar amount corresponding to this line's tax, based on subtotal and variants/subLines if applicable
  total: number // subtotal + tax + SUM(variants.priceChange) + SUM(subLines.priceChange)
  sublines: Subline[]
  itemType?: string
  cleaningService?: string
  pieces?: number
  minLbs?: number
  pricePerLb?: number
}

// For the MVP, the only used TicketStatus should be Closed
export enum TicketStatus {
  Open,
  Closed,
  Unclaimed,
  Voided,
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
  UNCLAIMED = 'UNCLAIMED',
  VOIDED = 'VOIDED',
}

// For the MVP, the only used PaymentStatus should be Paid
export enum PaymentStatus {
  Paid,
  Unpaid,
}

export interface TaxConfiguration {
  rates?: {
    [key: string]: number
  }
  calculationBase?: string
}

export interface TicketData {
  number?: number
  items: Item[]
  subtotal: number
  storeCustomerId?: number
  tax: number
  total: number
  totalMemo?: number
  totalDiscount?: number
  totalSurcharge?: number
  ticketStatus?: TicketStatus | TicketStatusForFilter
  paymentStatus?: PaymentStatus | PaymentStatusForFilter
  openDate?: Date | null
  dueDate?: Date | null
  closeDate?: Date | null
  priceAdjustments: PriceAdjustment[]
  memos: Memo[]
  billingEvents?: BillingEvent[]
  isFilter?: boolean
  totalAmountPaid?: number
  createTeamMemberName?: string
  notes?: string
  fulfillment?: Fulfillment
  balance?: number
  flagged?: boolean
  taxConfiguration?: TaxConfiguration
  rackId?: string
}

export interface TicketNdSalesProps {
  title?: string
}

export interface TextCurrencyProps {
  amount: number
  variant?: string
  className?: string
  sx?: ThemeUIStyleObject
}

export interface ItemLineProps {
  item: Item
  isSelected: boolean
  onClick: () => void
}

export interface CurrentItemLineProps {
  item: LineItem
  isSelected: boolean
  variantRef: React.RefObject<HTMLDivElement>
  itemWeightModalRef: React.RefObject<HTMLDivElement>
  serviceModalRef: React.RefObject<HTMLDivElement>
  selectedIndex: number | null
  onClick: () => void
  handleClickSaveEdit: () => void
  clearEmptySublines: () => void
  toggleVariantModal: (value: boolean) => void
  isItemSelected: boolean
  itemSelected: (value: boolean) => void
  currentTicket: Partial<CurrentTicketData>
  setSelectedItem: (value: selectedItemData | null) => void
  setSelectedItemIndex: (value: number | null) => void
  index: number
  onClickDelete: () => void
  isEditTicket: boolean
}

export interface CurrentPriceAdjustmentProps {
  priceAdjustment: PriceAdjustmentData
  isSelected: boolean
  selectedIndex: number | null
  onClick: () => void
  isItemSelected: boolean
  itemSelected: (value: boolean) => void
  currentTicket: Partial<CurrentTicketData>
  setSelectedPriceAdjustment: (
    value: SelectedPriceAdjustmentData | null
  ) => void
  setSelectedPriceAdjustmentIndex: (value: number | null) => void
}

export interface CurrentMemoProps {
  memo: Memo
  isSelected: boolean
  selectedIndex: number | null
  onClick: () => void
  isItemSelected: boolean
  itemSelected: (value: boolean) => void
  currentTicket: Partial<CurrentTicketData>
  setSelectedMemoIndex: (value: number | null) => void
}

export interface CurrentSublineProps {
  selectedIndex: number | null
  currentTicket: Partial<CurrentTicketData>
  isActive?: boolean
  isSelected: boolean
  index: number
  itemName: string
  itemPrice: number
  onClickSubline: (updatedSublines: Subline[]) => void
  onChangeSubline: (value: Subline[]) => void
  handleClickSaveEdit: () => void
  item: LineItem
  isAlterationVariant: (variantId: string) => boolean
  isItemDisabled: boolean
}

export enum SublineType {
  VARIANT = 'VARIANT',
  CUSTOM = 'CUSTOM',
}

export interface CommonSubline {
  type: SublineType
  comment?: string
  variantId?: string | null
  addOnName?: string
  quantity: number
  isAlterationMatched?: boolean
  isPreSet?: boolean
  deletedIndex?: number
  originalData?: {
    price: number | null
    comment?: string
    quantity: number
    addOnName?: string
    isAlterationMatched?: boolean
  }
}

export interface Subline extends CommonSubline {
  price: number | null
  uniqueId: string
}
export enum SublineOperationType {
  COMMENT = 'COMMENT',
  PRICE = 'PRICE',
}
export interface BackendSubline extends CommonSubline {
  price: number
}

export interface EditStateSubline extends Subline {
  action?: CRUD_OPERATION_TYPE
}

export type ItemType =
  | TextSale.SALES
  | TextSale.WEIGHTED
  | TextSale.OTHER
  | TextSale.CLEANING
  | TextSale.MAID
  | string

export interface CardData {
  expiry?: string
  token?: string
  errorMessage?: string
}

export enum CRUD_OPERATION_TYPE {
  ADD = 'ADD',
  EDIT = 'EDIT',
  DELETE = 'DELETE',
}

export enum EDIT_TICKET_ACTION_TYPE {
  ADD_LINE_ITEM = 'ADD_LINE_ITEM',
  EDIT_LINE_ITEM = 'EDIT_LINE_ITEM',
  DELETE_LINE_ITEM = 'DELETE_LINE_ITEM',

  ADD_SUBLINE = 'ADD_SUBLINE',
  EDIT_SUBLINE = 'EDIT_SUBLINE',
  DELETE_SUBLINE = 'DELETE_SUBLINE',

  ADD_PRICE_ADJUSTMENT = 'ADD_PRICE_ADJUSTMENT',
  EDIT_PRICE_ADJUSTMENT = 'EDIT_PRICE_ADJUSTMENT',
  DELETE_PRICE_ADJUSTMENT = 'DELETE_PRICE_ADJUSTMENT',

  ADD_MEMO = 'ADD_MEMO',
  EDIT_MEMO = 'EDIT_MEMO',
  DELETE_MEMO = 'DELETE_MEMO',

  EDIT_QUANTITY = 'EDIT_QUANTITY',
  CLEAR_EMPTY_SUBLINE = 'CLEAR_EMPTY_SUBLINE',
  REMOVE_FIRST_THEN_ADD_SUBLINE = 'REMOVE_FIRST_THEN_ADD_SUBLINE',
}

export interface CommonLineItem {
  itemId: string
  name: string
  quantity: number | null
  price: number
  itemType: ItemType
  subtotal: number
  taxRate: number // [0, 1] representing the % that should be charged as tax for this item
  tax: number
  isSelected?: boolean
  itemIndex?: number
  isQuantityModalOpen?: boolean
  minLbs?: number
  pricePerLb?: number
  pieces?: number
  cleaningService?: string
  isPreSet?: boolean
  deletedIndex?: number
  originalData?: {
    price?: number
    quantity?: number
    cleaningService?: string
  }
}

export interface LineItem extends CommonLineItem {
  sublines: Subline[]
  uniqueId: string
}

export interface BackendLineItem extends CommonLineItem {
  sublines: BackendSubline[]
}

export interface EditStateLineItem extends CommonLineItem {
  sublines: EditStateSubline[]
  uniqueId: string
  action?: CRUD_OPERATION_TYPE
}

export interface BackendEditStateLineItem extends CommonLineItem {
  sublines: Partial<EditStateSubline>[]
  uniqueId: string
  action?: CRUD_OPERATION_TYPE
}

export interface PriceAdjustmentData {
  priceAdjustmentId?: number
  type: string
  name: string
  rate: number
  subtotal: number
  tax: number
  comment: string
  priceAdjustmentIndex?: number
}

export interface ValidCardData {
  cardExpiry: string
  cardPaymentToken: string
}

export interface Memo {
  type: string
  name: string
  subtotal: number
  comment: string
  memoIndex?: number
}

export interface PaymentIncentive {
  type?: string
  name?: string
  rate?: number
  adjustedBalancePayment?: number
  originalBalancePayment?: number
  amount?: number
  subtotal?: number
  tax?: number
}

export interface CardEmvTagData {
  TVR?: string
  PIN?: string
  Signature?: string
  Mode?: string
  'Network Label'?: string
  AID?: string
  IAD?: string
  'Entry method'?: string
  'Application Label'?: string
}
export interface CommonPaymentData extends ManualCreditCardPaymentData {
  amount?: number
  change?: number
  tip?: number
  paymentIncentive?: PaymentIncentive
  checkNumber?: string
  cardPaymentToken?: string
  cardExpiry?: string
  cardAuthCode?: string
  cardEntryMode?: string
  cardLastFour?: string
  cardPaymentRetRef?: string
  cardResponseText?: boolean
  cardEmvTagData?: CardEmvTagData
  otherMethod?: string
}
export interface PaymentDetail extends CommonPaymentData {
  type: PaymentMethodType
}

export interface BillingEventPaymentDetails extends CommonPaymentData {
  paymentType?: PaymentMethodType
  paymentAmountTendered?: number
  paymentAmountChange?: number
  notes?: string
  reason?: string
  reasonDetails?: string
}

export interface Payment {
  total: number
  payments?: PaymentDetail[]
}

// TODO(alberto): Cleanup if not used after all the calculations are implemented
export interface Discount {
  percentage: number // 0-1 bound percentage, eg. 10% would be 0.1
}

export interface SelectedIndexes {
  lineItemIndex?: number
  sublineIndex?: number
  priceAdjustmentIndex?: number
  memoIndex?: number
  sublinesIndex?: number[]
}
export interface CommonTicketData {
  dryRun?: boolean
  storeCustomerId?: number
  createTeamMemberId?: string
  priceAdjustments?: PriceAdjustmentData[]
  memos?: Memo[]
  taxConfiguration: TaxConfiguration
  totalDiscount: number
  totalSurcharge: number
  totalMemo: number
  subtotal: number
  taxTotal: number
  total: number
  notes?: string
  flagged?: boolean
  isEdit?: boolean
  currentTicketId?: number
  isPreSet?: boolean
}
export interface CurrentTicketData extends CommonTicketData {
  lineItems: LineItem[]
  fulfillment?: Fulfillment
  uniqueId?: number
  originalData?: {
    flagged?: boolean
    notes?: string
  }
}

export enum LineItemState {
  Blurred = 'Blurred',
  Focused = 'Focused',
  Clicked = 'Clicked',
}

export enum NoteState {
  Focused = 'Focused',
  Blurred = 'Blurred',
}

export enum ButtonState {
  Clicked = 'Clicked',
}

export interface EditTicketData extends CommonTicketData {
  action?: CRUD_OPERATION_TYPE
  lineItems: EditStateLineItem[]
  fulfillment?: Fulfillment
}

export interface BackendEditTicketData extends CommonTicketData {
  action?: CRUD_OPERATION_TYPE
  lineItems: Partial<BackendEditStateLineItem>[]
  fulfillment?: Fulfillment
  teamMemberId: string
}

export interface BackendCurrentTicketData extends CommonTicketData {
  lineItems: BackendLineItem[]
  fulfillment?: Fulfillment | {}
}

export const EMPTY_LINE_ITEM: LineItem = {
  itemId: '',
  name: '',
  quantity: 1,
  price: 0,
  itemType: '',
  sublines: [],
  subtotal: 0,
  taxRate: 0,
  tax: 0,
  isSelected: false,
  uniqueId: uuidv4(),
}

export const EMPTY_CURRENT_TICKET_DATA: CurrentTicketData = {
  storeCustomerId: 0,
  lineItems: [],
  taxConfiguration: {},
  totalDiscount: 0,
  totalSurcharge: 0,
  totalMemo: 0,
  subtotal: 0,
  taxTotal: 0,
  total: 0,
  notes: '',
}

export const EMPTY_PRICE_ADJUSTMENT: PriceAdjustmentData = {
  priceAdjustmentId: 0,
  type: '',
  name: '',
  rate: 0,
  subtotal: 0,
  tax: 0,
  comment: '',
}

export const EMPTY_PAYMENT_INCENTIVE: PaymentIncentive = {
  type: '',
  name: '',
  rate: 0,
  adjustedBalancePayment: 0,
  amount: 0,
  originalBalancePayment: 0,
}

export const EMPTY_MEMO: Memo = {
  type: '',
  name: '',
  subtotal: 0,
  comment: '',
}

// Type of the ticket coming from the API.

export interface TicketItemsBE {
  itemId: string
  name: string
  quantity: number
  price: number
  itemType?: string
  subtotal: number
  tax: number
  sublines: Subline[]
  total?: number
  unitPrice?: number
  cleaningService?: string
  pieces?: number
  minLbs?: number
  pricePerLb?: number
}
interface TaxConfigurationBE {
  rate: number
  calculationBase: string
}

interface PaymentDetailBE {
  type: string
  amount: number
  change: number
  tip: number
}

export interface RefundBE {
  refundMethod: RefundMethod
  reason: string
  reasonDetails: string
  includeComp: boolean
}

export interface PaymentBE {
  total: number
  payments: PaymentDetailBE[]
  refunds?: RefundBE[]
}

export interface BillingEvent {
  eventTimestamp: string
  eventType: 'PAYMENT' | 'REFUND' | 'UPDATE'
  amount: number
  details: BillingEventPaymentDetails
}

export interface TicketDataBE {
  ticketId: number
  createTimestamp: string
  ticketStatus: TicketStatusForFilter
  paidStatus: PaymentStatusForFilter
  balance: number
  storeId: number
  storeCustomerId: number
  lineItems: TicketItemsBE[]
  taxConfiguration: TaxConfigurationBE
  totalDiscount: number
  totalSurcharge: number
  totalMemo: number
  subtotal: number
  taxTotal: number
  total: number
  priceAdjustments?: PriceAdjustment[]
  memos: Memo[]
  billingEvents: BillingEvent[]
  totalAmountPaid: number
  closeDate?: string
  closeTimestamp?: string
  createTeamMemberName?: string
  fulfillment?: Fulfillment
  flagged?: boolean
  openDate?: string
  notes?: string
  rackId?: string
}

export interface QuickCheckoutTicketData extends TicketData {
  isSelected: boolean
}

export interface ManualCreditCardPaymentData {
  cardHolderName?: string
  cardHolderZipCode?: string
  isManualEntry?: boolean
}

export enum PaymentMethodType {
  CASH = 'CASH',
  CREDIT_CARD = 'CREDIT_CARD',
  CHECK = 'CHECK',
  PAY_LATER = 'PAY_LATER',
  STORE_CREDIT = 'STORE_CREDIT',
  OTHER = 'OTHER',
}

export interface PaidAmountData extends ManualCreditCardPaymentData {
  type: PaymentMethodType
  amount: number
  checkNumber?: string
  cardPaymentToken?: string
  cardExpiry?: string
  otherMethod?: string
}

export interface Comp {
  addTimestamp: string
  subtotal: number
  tax: number
}

export enum RefundReason {
  CUSTOMER_DISSATISFACTION = 'CUSTOMER_DISSATISFACTION',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  OTHER = 'OTHER',
}

export interface RefundOptions {
  label: string
  value: string
}

export const refundOptions: RefundOptions[] = [
  {
    value: RefundReason.CUSTOMER_DISSATISFACTION,
    label: translation.CUSTOMER_DISSATISFACTION,
  },
  {
    value: RefundReason.INTERNAL_ERROR,
    label: translation.INTERNAL_ERROR,
  },
  {
    value: RefundReason.OTHER,
    label: translation.OTHER,
  },
]

export enum RefundMethod {
  CREDIT_CARD = 'CREDIT_CARD',
  CHECK = 'CHECK',
  CASH = 'CASH',
  STORE_CREDIT = 'STORE_CREDIT',
  PAY_LATER = 'PAY_LATER',
}

export interface RefundTicket {
  ticketIds: number[]
  action: string
  refund: {
    includeComp: boolean
    refundMethod: PaymentMethodType
    reason: RefundReason
    reasonDetails?: string
  }
}

export interface PrintTicket {
  variantsList: Variant[]
  customer: SelectedCustomerDetail
  ticketSettingsData: TicketSettings
  teamMember: TeamMember
  store: Store
  ticketData: TicketDataBE
  isStoreInformation: boolean
  isTicketBarcode: boolean
  isAddOnPrices: boolean
  stmData: string
  isStmEditor: boolean
}

export type SelectedCustomerDetail = {
  name: string
  address: string
  phoneNumber: string
}

export enum SettingsSubTicketsType {
  WET_CLEAN = 'wetCleanGroupedItem',
  DRY_CLEAN = 'dryCleanGroupedItem',
  CLEANING_WEIGHT = 'weightedGroupedItem',
  ALTERATION = 'alterationGroupedItem',
}

export interface STMConfigType {
  option: PrintOptions
  isStmEditor?: boolean
  stmData?: string
  ticketNumber: string
  notes: string
  adjustedDate: string | null
  customer: SelectedCustomerDetail
  fulfillmentDay: string | null
  isRushEnabled: boolean
  isStoreInfoEnabled: boolean
  isCustomerAddressEnabled: boolean
  isCustomerPhoneNumberEnabled: boolean
  isTicketBarcodeEnabled: boolean
  isAddOnPricesEnabled: boolean
  isMessageOnTicketEnabled: boolean
  ticketMessage?: string
  lineItems: LineItem[]
  orderGroupedItemsData: GroupedItemsPrintTicket[]
  storeCopy: {
    numberOfPrintCopies: number
  }
  ticketSummary: {
    subtotal: string
    tax: string
    total: string
    OutstandingBalance: string
  }
  subTicket: {
    isDryCleanEnabled: boolean
    isWetCleanEnabled: boolean
    isCleaningWeightEnabled: boolean
    isAlterationEnabled: boolean
    modifiedAlterationLineItems: TicketItemsBE[] | undefined
    subTicketItems: {
      wetClean: ModifiedData
      dryClean: ModifiedData
      weighted: ModifiedData
      wetCleanGroupedItem: GroupedItemsPrintTicket[]
      dryCleanGroupedItem: GroupedItemsPrintTicket[]
      weightedGroupedItem: GroupedItemsPrintTicket[]
      alterationGroupedItem: GroupedItemsPrintTicket[]
      wetCleanGroupedItemPieces: number
      dryCleanGroupedItemPieces: number
      alterationGroupedItemPieces: number
    }
  }
  storeData: {
    teamMemberName: string
    isPaymentReceipt: boolean
  }
  store: Store
  paymentDetails: {
    billingEvents?: BillingEvent[]
    totalAmountPaid: number
    ticketAndPaidAmount?: { ticketId: string; totalAmountPaid: number }[]
    totalPaidAmount?: number
    paymentAmountTendered?: number
    cardAuthCode?: string
    cardLastFour?: string
    cardEntryMode?: string
    paymentAmountChange?: number
    cardPaymentRetRef?: string
    cardEmvTagData?: CardEmvTagData
    cardResponseText?: boolean
    paymentType?: PaymentMethodType
    eventType?: 'PAYMENT' | 'REFUND' | 'UPDATE'
    checkNumber?: string
    otherMethod?: string
  }
  isPrintPaymentReceipt?: boolean
  fulfillmentDetail: Fulfillment | null
  fulfillmentDate: Date | null
  numberOfPieces: number | undefined
  selectedTeamMember: TeamMember
}

export interface ModifiedData {
  lineItems: TicketItemsBE[] | undefined
  totalQuantity: number
}

export interface SubTicket {
  isDryCleanEnabled: boolean
  isWetCleanEnabled: boolean
  isCleaningWeightEnabled: boolean
  isAlterationEnabled: boolean
  modifiedAlterationLineItems: LineItem[]
  subTicketItems: SubTicketItem
}

export interface SubTicketItem {
  wetClean?: TicketData
  dryClean?: TicketData
  weighted?: TicketData
  wetCleanGroupedItem?: GroupedItemsPrintTicket[]
  dryCleanGroupedItem?: GroupedItemsPrintTicket[]
  weightedGroupedItem?: GroupedItemsPrintTicket[]
}

export enum TicketLayoutSetting {
  STORE_INFORMATION = 'STORE_INFORMATION',
  TICKET_BARCODE = 'TICKET_BARCODE',
  ITEM_ADD_ON_PRICES = 'ADDON_PRICES',
}

export interface TicketPrintTransFormDataType {
  variantsList?: Variant[]
  customersList?: Customers[]
  ticketSettingsData?: TicketSettings | null
  store: Store
  ticketData: Partial<TicketDataBE> | null
  isStoreInformation: boolean
  isTicketBarcode: boolean
  isAddOnPrices: boolean
  copies: number
  option: PrintOptions
  selectedTeamMember: TeamMember
}

export const excludedFieldMapping: Record<string, (keyof LineItem)[]> = {
  [TextSale.CLEANING]: ['minLbs', 'pricePerLb'],
  [TextSale.WEIGHTED]: ['cleaningService', 'pieces'],
  [TextSale.MAID]: ['cleaningService', 'pieces', 'minLbs', 'pricePerLb'],
  [TextSale.SALES]: ['cleaningService', 'pieces', 'minLbs', 'pricePerLb'],
  [TextSale.OTHER]: ['cleaningService', 'pieces', 'minLbs', 'pricePerLb'],
}

export enum PaymentTypes {
  PAYMENT = 'PAYMENT',
  REFUND = 'REFUND',
  CHECKOUT = 'CHECKOUT',
  FLAG = 'FLAG',
  REOPEN = 'REOPEN',
  VOID = 'VOID',
  REMOVE_INVENTORY = 'REMOVE_INVENTORY',
  EDIT_TICKET = 'EDIT_TICKET',
}

export enum UpdateTicketActions {
  REMOVE_INVENTORY = 'REMOVE_INVENTORY',
}

export enum ticketCustomerTableColumns {
  NUMBER = 'number',
  STATUS = 'status',
  STORE_CUSTOMER_ID = 'storeCustomerId',
  TOTAL = 'total',
  TICKET_ID = 'ticketId',
}

export interface GroupedItemsPrintTicket {
  name: string
  items: TicketItemsBE[]
  totalQuantity: number
}

export interface CommonConfirmationProps {
  isOpen: boolean
  onClose: () => void
  onClickSubmit: () => void
  closeIcon?: string
  modalContainer?: string
  submitButtonText?: string
  title: string
  description: string
  isLoading: boolean
  showCloseIcon?: boolean
  modal?: React.CSSProperties
}
export interface VoidDetails {
  voidReason: string
}

export interface RefundDataProps {
  refundMethod: PaymentMethodType
  reason: RefundReason
  reasonDetails: string
  includeComp: boolean
}

export enum TicketAction {
  CREATE_TICKET = 'Create Ticket',
  QUICK_DROP_OFF = 'Quick Dropoff',
}
