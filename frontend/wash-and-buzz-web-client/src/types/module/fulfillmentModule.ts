import { FulfillmentMethod } from '@/components/Fulfillment/FulfillmentMethodButton'
import { PaidAmountData, TicketData } from '@/types/module/ticketNdSalesModule'

export interface FulfillmentTabs {
  tabName: string
  displayName: string
}

export interface Fulfillment {
  method: FulfillmentMethod
  rush: boolean
  date: string
  time: number
}

export enum GeneralFulfillment {
  RUSH = 'Rush',
  LOAD_MANAGEMENT = 'Load Management',
  STORAGE = 'Storage',
}

export interface LoadType {
  name: string
  defaultValue: number
}

export interface GeneralTabData {
  label: string
  description: string
  loadTypes?: LoadType[]
}

export interface GeneralTabItem {
  label: GeneralFulfillment
  description: string
  loadTypes?: LoadType[]
}

export type FulfillmentSettings = {
  rush: {
    fee: {
      enabled: boolean
      amount: number
    }
  }
  loadManagement: {
    colorCoding: {
      enabled?: boolean
      settings?: Array<{
        option?: 'DRY_CLEAN' | 'WET_CLEAN' | 'WEIGHTED'
        amount?: number
      }>
      setting?: Array<{
        option?: 'DRY_CLEAN' | 'WET_CLEAN' | 'WEIGHTED'
        amount?: number
      }>
    }
  }
  storePickup: {
    times: number[]
    defaultDays: number
  }
  rackManagement: {
    allowMultiTicketRackAssignment: boolean
  }
}

export enum LoadManagementSettings {
  DRY_CLEAN = 'Dry cleaning',
  LAUNDRY = 'Laundry',
  WEIGHTED = 'Cleaning (Weight)',
}

export enum FulfillmentSettingsLabel {
  RUSH = 'Rush',
  LOAD_MANAGEMENT = 'Load Management',
}

export enum FulfillmentSettingsDescription {
  RUSH = 'Enable rush fee',
  LOAD_MANAGEMENT = 'Enable color-code for load management',
}

export interface FulfillmentSettingsPayload
  extends Partial<FulfillmentSettings> {
  operationsId: string
  storeId: number
}

export interface ColorCodingSetting {
  option: string
  amount: number
}

export interface LoadManagementSettingsArgs {
  enabled?: boolean
  settings?: ColorCodingSetting[]
}

export interface loadManagementSettingsChangesArgs {
  loadManagement: {
    colorCoding: {
      enabled?: boolean
      settings?: {
        option?: string
        amount?: number
      }
      setting?: {
        option?: string
        amount?: number
      }
    }
  }
}
export interface StoreTimeModalProps {
  isOpen: boolean
  onClose: () => void
  closeIcon?: string
  modalContainer?: string
  title: string
  showCloseIcon?: boolean
  modal?: React.CSSProperties
  isEdit?: boolean
  editData?: { time: number; index: number } | null
  pickupTimes: number[]
  setPickupTimes: (value: number[] | ((prev: number[]) => number[])) => void
}

export enum LoadManagementSettingsFields {
  DRY_CLEAN = 'DRY_CLEAN',
  LAUNDRY = 'LAUNDRY',
  WEIGHTED = 'WEIGHTED',
}

export interface FulfillmentModalProps {
  isOpen: boolean
  onClose: () => void
  closeIcon?: string
  defaultSelectedMethod?: FulfillmentMethod
  modalContainer?: string
  title: string
  showCloseIcon?: boolean
  loading: boolean
  handleSave: (
    paidAmount: PaidAmountData | null,
    fulfillment?: Fulfillment
  ) => void
  handlePrepay: (fulfillment: Fulfillment) => void
  actualTicketData?: TicketData
  isQuickDropoff?: boolean
}

export interface ColorCodingSettings {
  enabled: boolean
  settings?: Array<{
    option?: LoadManagementSettingsFields
    amount?: number
  }>
}
