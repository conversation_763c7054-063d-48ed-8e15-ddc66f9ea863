import { DateRangeSelectionProps } from '@/types/module/dateRangeCalendarModule'
import { RangeKeyDict } from 'react-date-range'
export interface GetSalesReportsSummary {
  storeId?: number
  dateRangeType?: string
  startdate: string
  enddate: string
}

export interface SalesReportsSummaryTransformData {
  id?: number
  dateRangeType?: string
  startDate: string
  endDate: string
  sales?: Sales
  ticket?: Ticket
  itemType?: ItemType
  paymentType?: PaymentType
}

export interface SalesSummaryOverviewData {
  amount: number
  label: string
  icon: string
  type?: string
  access?: boolean
}

export interface Sales {
  gross: number
  net: number
  total: number
  discountTotal: number
  taxTotal: number
}

export interface Ticket {
  issued: number
  paid: number
  unpaid: number
}

export interface ItemType {
  cleaning: number
  maid: number
  other: number
  sales: number
  weighted: number
}
export interface PaymentType {
  cash: number
  creditCard: number
  check: number
  storeCredit?: number
  other?: number
}

export interface GrossSalesOverview {
  date: string
  gross: number
}
export interface NetSalesOverview {
  date: string
  net: number
}
export interface TipSalesOverview {
  date: string
}
export interface TotalSalesOverview {
  date: string
  total: number
}
export interface IssuedTicketOverview {
  date: string
  issued: number
}
export interface PaidIssuedTicketOverview {
  date: string
  paid: number
}
export interface UnpaidIssuedTicketOverview {
  date: string
  unpaid: number
}
export interface SalesItemType {
  date?: string
  sales?: number
}
export interface OthersItemType {
  date: string
  other: number
}
export interface CashPaymentType {
  date: string
  cash: number
}
export interface CardPaymentType {
  date: string
  creditCard: number
}
export interface CheckPaymentType {
  date: string
  check: number
}

export interface SalesReportsData {
  id?: number
  dateRangeType?: string
  startDate?: string
  endDate?: string
  sales?: {
    gross: number
    net: number
    total: number
    discountTotal: number
    taxTotal: number
  }
  ticket?: {
    issued: number
    paid: number
    unpaid: number
  }
  itemType?: {
    cleaning: number
    maid: number
    other: number
    sales: number
    weighted: number
  }
  paymentType?: {
    cash: number
    creditCard: number
    check: number
    storeCredit?: number
    other?: number
  }
  grossSalesOverview?: GrossSalesOverview[]
  netSalesOverview?: NetSalesOverview[]
  tipSalesOverview?: TipSalesOverview[]
  totalSalesOverview?: TotalSalesOverview[]
  issuedTicketOverview?: IssuedTicketOverview[]
  paidIssuedTicketOverview?: PaidIssuedTicketOverview[]
  unpaidIssuedTicketOverview?: UnpaidIssuedTicketOverview[]
  salesItemType?: SalesItemType[]
  othersItemType?: OthersItemType[]
  cashPaymentType?: CashPaymentType[]
  cardPaymentType?: CardPaymentType[]
  checkPaymentType: CheckPaymentType[]
  type?: string
}

export interface GetSalesAnalysisSummary {
  storeId?: number
  timeOffset?: number
  dateRangeType?: string
  startDate?: string
  endDate?: string
  type?: string
}

export interface SalesAnalysisSummaryTransformData {
  id?: number
  dateRangeType: string
  startDate: string
  endDate: string
  sales?: Sales
  ticket?: Ticket
  itemType?: ItemType
  paymentType?: PaymentType
}

export interface GetAnalyticsReportsApi {
  storeId?: number
  timeOffset?: number
  dateRangeType?: string
  startDate: string
  endDate: string
  type?: string
}

// Analytics flow types

export interface AnalyticsOverviewProps {
  isAnalyticsPage?: boolean
  setIsAnalyticsPage: (value: boolean) => void
  setAnalyticsPageApiData: (value: GetAnalyticsReportsApi) => void
}

export interface AnalyticsReportsData {
  date?: string
  issued?: number
  gross?: number
  net?: number
  total?: number
  paid?: number
  unpaid?: number
  sales?: number
  other?: number
  cash?: number
  creditCard?: number
  check?: number
}

export interface AnalyticsReportsApiData {
  id?: number
  dateRangeType?: string
  startDate?: string
  endDate?: string
  sales?: AnalyticsReportsData[]
  ticket?: AnalyticsReportsData[]
  itemType?: AnalyticsReportsData[]
  paymentType?: AnalyticsReportsData[]
}

export interface AnalyticsDataPageProps {
  analyticsPageApiData: GetAnalyticsReportsApi | null
}

export interface GraphAnalyticsProps {
  graphApiData: GetAnalyticsReportsApi | null
  param: string
  identifier?: string
}

export interface GraphAnalyticsApiResponse {
  sales: AnalyticsReportsData[]
  ticket: AnalyticsReportsData[]
  itemType: AnalyticsReportsData[]
  paymentType: AnalyticsReportsData[]
}

export interface GraphAnalyticsIDBData extends GraphAnalyticsMultipleData {
  id?: number
  startDate: string
  endDate: string
  dateRangeType: string
}

export interface GraphAnalyticsMultipleData {
  grossSalesOverview: AnalyticsReportsData[]
  netSalesOverview: AnalyticsReportsData[]
  tipSalesOverview: AnalyticsReportsData[]
  totalSalesOverview: AnalyticsReportsData[]
  issuedTicketOverview: AnalyticsReportsData[]
  paidIssuedTicketOverview: AnalyticsReportsData[]
  unpaidIssuedTicketOverview: AnalyticsReportsData[]
  salesItemType: AnalyticsReportsData[]
  othersItemType: AnalyticsReportsData[]
  cashPaymentType: AnalyticsReportsData[]
  cardPaymentType: AnalyticsReportsData[]
  checkPaymentType: AnalyticsReportsData[]
}

export enum AnalyticsTypes {
  'sales-gross' = 'grossSalesOverview',
  'sales-net' = 'netSalesOverview',
  'sales-tip' = 'tipSalesOverview',
  'sales-total' = 'totalSalesOverview',
  'tickets-issued' = 'issuedTicketOverview',
  'tickets-paid' = 'paidIssuedTicketOverview',
  'tickets-unpaid' = 'unpaidIssuedTicketOverview',
  'itemtype-sales' = 'salesItemType',
  'itemtype-other' = 'othersItemType',
  'payment-cash' = 'cashPaymentType',
  'payment-credit-card' = 'cardPaymentType',
  'payment-check' = 'checkPaymentType',
}

export interface SalesReportsDateRangeProps {
  today: Date
  selectedDateRange: DateRangeSelectionProps[]
  handleOutsideClick: (date1: Date, date2: Date) => void
  handleCustomButtonClick: () => void
  selectedRangeType: string
  showDatePicker: boolean
  handleSelect: (value: RangeKeyDict) => void
  handlePredefinedDateRange: (value: string) => void
}
