:root {
  --select-store-bg-color: #f2f2f2;
  --text-element-subdued-dark: #858585;
  --primary-color: #303030;
  --white: #ffffff;
  --white-smoke: #f2f2f2;
  --bright-gray: #eaeaea;
  --brand-secondary: #00c6b7;
}

.main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 6rem;
  min-height: 100vh;
}

.description {
  display: inherit;
  justify-content: inherit;
  align-items: inherit;
  font-size: 0.85rem;
  max-width: var(--max-width);
  width: 100%;
  z-index: 2;
  font-family: var(--font-mono);
}

.description a {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.description p {
  position: relative;
  margin: 0;
  padding: 1rem;
  background-color: rgba(var(--callout-rgb), 0.5);
  border: 1px solid rgba(var(--callout-border-rgb), 0.3);
  border-radius: var(--border-radius);
}

.code {
  font-weight: 700;
  font-family: var(--font-mono);
}

button {
  outline: none !important;
}

.grid {
  display: grid;
  grid-template-columns: repeat(4, minmax(25%, auto));
  max-width: 100%;
  width: var(--max-width);
}

.card {
  padding: 1rem 1.2rem;
  border-radius: var(--border-radius);
  background: rgba(var(--card-rgb), 0);
  border: 1px solid rgba(var(--card-border-rgb), 0);
  transition:
    background 200ms,
    border 200ms;
}

.card span {
  display: inline-block;
  transition: transform 200ms;
}

.card h2 {
  font-weight: 600;
  margin-bottom: 0.7rem;
}

.card p {
  margin: 0;
  opacity: 0.6;
  font-size: 0.9rem;
  line-height: 1.5;
  max-width: 30ch;
  text-wrap: balance;
}

.center {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 4rem 0;
}

.center::before {
  background: var(--secondary-glow);
  border-radius: 50%;
  width: 480px;
  height: 360px;
  margin-left: -400px;
}

.center::after {
  background: var(--primary-glow);
  width: 240px;
  height: 180px;
  z-index: -1;
}

.center::before,
.center::after {
  content: '';
  left: 50%;
  position: absolute;
  filter: blur(45px);
  transform: translateZ(0);
}

.logo {
  position: relative;
}

/* Enable hover only on non-touch devices */
@media (hover: hover) and (pointer: fine) {
  .card:hover {
    background: rgba(var(--card-rgb), 0.1);
    border: 1px solid rgba(var(--card-border-rgb), 0.15);
  }

  .card:hover span {
    transform: translateX(4px);
  }
}

@media (prefers-reduced-motion) {
  .card:hover span {
    transform: none;
  }
}

/* Mobile */
@media (max-width: 700px) {
  .content {
    padding: 4rem;
  }

  .grid {
    grid-template-columns: 1fr;
    margin-bottom: 120px;
    max-width: 320px;
    text-align: center;
  }

  .card {
    padding: 1rem 2.5rem;
  }

  .card h2 {
    margin-bottom: 0.5rem;
  }

  .center {
    padding: 8rem 0 6rem;
  }

  .center::before {
    transform: none;
    height: 300px;
  }

  .description {
    font-size: 0.8rem;
  }

  .description a {
    padding: 1rem;
  }

  .description p,
  .description div {
    display: flex;
    justify-content: center;
    position: fixed;
    width: 100%;
  }

  .description p {
    align-items: center;
    inset: 0 0 auto;
    padding: 2rem 1rem 1.4rem;
    border-radius: 0;
    border: none;
    border-bottom: 1px solid rgba(var(--callout-border-rgb), 0.25);
    background: linear-gradient(
      to bottom,
      rgba(var(--background-start-rgb), 1),
      rgba(var(--callout-rgb), 0.5)
    );
    background-clip: padding-box;
    backdrop-filter: blur(24px);
  }

  .description div {
    align-items: flex-end;
    pointer-events: none;
    inset: auto 0 0;
    padding: 2rem;
    height: 200px;
    background: linear-gradient(
      to bottom,
      transparent 0%,
      rgb(var(--background-end-rgb)) 40%
    );
    z-index: 1;
  }
}

/* Tablet and Smaller Desktop */
@media (min-width: 701px) and (max-width: 1120px) {
  .grid {
    grid-template-columns: repeat(2, 50%);
  }
}

@media (prefers-color-scheme: dark) {
  .vercelLogo {
    filter: invert(1);
  }

  .logo {
    filter: invert(1) drop-shadow(0 0 0.3rem #ffffff70);
  }
}

@keyframes rotate {
  from {
    transform: rotate(360deg);
  }

  to {
    transform: rotate(0deg);
  }
}

.menu {
  flex: 1;
  padding: 20px;
}

.content {
  flex: 4;
  padding: 20px;
}

.main-container {
  display: flex;
}

.menu {
  flex: 1;
  padding: 20px;
}

.content {
  flex: 4;
  padding: 20px;
}

.sidebar {
  position: sticky;
  top: 40px;
}

.menu-link {
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.margin-top-bottom {
  margin: 10px 0px;
}

/*--------------------------------Custom Scroll css--------------------------------*/

/* Define scrollbar track */

.custom-scroll ::-webkit-scrollbar-track {
  background-color: #f2f2f2;
  border-radius: 4px;
}

/* Define scrollbar thumb */
.custom-scroll ::-webkit-scrollbar-thumb {
  background-color: #a6a6a6;
  border-radius: 10px;
  border: 5.5px solid transparent;
  background-clip: content-box;
}

/* Define scrollbar corner */
.custom-scroll ::-webkit-scrollbar-corner {
  background-color: #fff;
}

/* Define scrollbar size */
.custom-scroll ::-webkit-scrollbar {
  width: 17px;
}

/* Define custom scrollbar track for variant box*/

.custom-variant-box-scroll ::-webkit-scrollbar-track {
  background-color: #f2f2f2 !important;
  border-radius: 4px !important;
}

/* Define scrollbar thumb */
.custom-variant-box-scroll ::-webkit-scrollbar-thumb {
  background-color: #a6a6a6 !important;
  border-radius: 10px !important;
  border: 4.5px solid transparent !important;
  background-clip: content-box !important;
}

/* Define scrollbar corner */
.custom-variant-box-scroll ::-webkit-scrollbar-corner {
  background-color: #fff !important;
}

/* Define scrollbar size */
.custom-variant-box-scroll ::-webkit-scrollbar {
  width: 14px !important;
}

/*--------------------------------Custom Scroll css ends--------------------------------*/

.cursor-pointer {
  cursor: pointer;
}

.cursor-not-allowed {
  cursor: not-allowed !important;
}

.font-noto-sans {
  font-family: Noto Sans !important;
}

input:focus {
  outline: none !important;
}

.light-border {
  border: 1px solid #eaeaea;
}

.css-tmir64-Checkbox {
  color: #00c6b7 !important;
}

.custom-slider-wrapper .slick-prev,
.slick-next {
  display: none !important;
}

.box-shadow-left {
  box-shadow: 4px 0px 20px 0px rgba(41, 113, 137, 0.1);
}

.box-shadow-right {
  box-shadow: 4px 0px 20px 0px rgba(41, 113, 137, 0.1);
}

.padding-around {
  padding: 30px 20px;
}

.padding-customer-side-menu {
  padding: 30px;
}

button {
  cursor: pointer;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 0;
}

button:hover {
  cursor: pointer;
}

.cta-button {
  height: 44px;
  font-family: Poppins;
  font-weight: 500;
  text-transform: capitalize;
}

.primary-button-hover {
  background-color: #20586b !important;
}

.primary-button-focus {
  padding: 2px !important;
  border: 1px solid var(--Brand-Secondary-Color---00C6B7, #00c6b7) !important;
}

.primary-button-active {
  background-color: #00c6b7 !important;
}

.secondary-button-hover {
  box-shadow: 0px 0px 10px 0px rgba(41, 113, 137, 0.2);
}

.secondary-button-active {
  background-color: #00c6b7 !important;
  color: #fff !important;
}

.tertiary-button-hover {
  color: #a6a6a6 !important;
  box-shadow: 0px 0px 10px 0px rgba(41, 113, 137, 0.2);
}

.tertiary-button-focused {
  color: #a6a6a6 !important;
}

.quaternary-button-active {
  background-color: #def0f6 !important;
}

.square-button {
  font-weight: 500 !important;
  border-radius: 4px;
}

.font-weight-600 {
  font-weight: 600 !important;
}

.clickable-text {
  position: relative;
  height: 44px;
  font-family: Poppins;
  font-weight: 500;
  border-radius: 4px;
}

.clickable-text svg {
  stroke: #2174d4;
}

.clickable-text:disabled svg {
  stroke: #858585;
}

.clickable-text:hover::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  left: 50%;
  transform: translateX(-50%);
  bottom: 10px;
  background-color: #2174d4;
}

.clickable-text:focus {
  color: #7cb3f1;
  text-decoration-line: none;
}

.clickable-text:focus svg {
  stroke: #7cb3f1 !important;
  text-decoration-line: none;
}

.clickable-text:focus:hover::after,
.clickable-text:disabled:hover::after {
  content: none;
}

.clickable-text:disabled {
  color: #a6a6a6;
  text-decoration-line: none;
  cursor: not-allowed;
}

.clickable-text-hover::after {
  content: '';
  position: absolute;
  width: 80%;
  height: 1px;
  left: 50%;
  transform: translateX(-50%);
  bottom: 10px;
  background-color: #2174d4;
}

.clickable-text-selected {
  color: #7cb3f1 !important;
  text-decoration-line: none !important;
}

.clickable-text-selected {
  color: #7cb3f1 !important;
  text-decoration-line: none !important;
}

/* clickable text without icon */

.clickable-text-only {
  font-family: Poppins;
  font-size: 16px;
  font-weight: 500;
  color: #2174d4;
  cursor: pointer;
}

.btn-height-44 {
  height: 44px;
}

.clickable-text-only:hover {
  text-decoration-line: underline;
}

.clickable-text-only-hover {
  min-width: 104px !important;
  height: 44px;
  font-family: Poppins;
  font-size: 16px;
  font-weight: 500;
  text-decoration-line: underline !important;
}

.clickable-text-only:active {
  color: #7cb3f1 !important;
  text-decoration-line: none;
}

.clickable-text-only:disabled {
  color: #a6a6a6;
  text-decoration-line: none;
  cursor: not-allowed;
}

.only-text {
  font-family: Poppins;
  font-size: 16px;
  font-weight: 500;
  color: #2174d4;
}

.clickable-text-notoSans {
  font-family: Noto Sans;
  font-weight: 600;
  color: #2174d4;
  cursor: pointer;
}

.text-inter {
  font-family: Inter;
}

.footer-position {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
}

.padding-ticket-sales {
  padding: 30px;
}

.sales-box {
  width: 60%;
}

.ticket-box {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.primary-input-icon {
  width: '100%';
  height: '100%';
  margin-left: -30px;
  max-width: '17.143px';
  min-width: '17.143px';
  max-height: '17.15px';
  min-height: '17.15px';
  align-self: 'center';
  pointer-events: 'none';
}

.primary-selection {
  width: '100%';
  height: '100%';
  min-width: '20px';
  max-width: '20px';
  max-height: '20px';
  min-height: '20px';
}

.price-list-table-height {
  max-height: calc(100vh - 214px);
  padding-right: 10px;
}

/* Common classes padding */

.ps-10 {
  padding-left: 10px !important;
}

.py-30 {
  padding-top: 30px !important;
  padding-bottom: 30px !important;
}

.py-4px {
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}

.mt-30 {
  margin-top: '30px' !important;
}

.mb-30 {
  margin-bottom: '30px' !important;
}

.px-10 {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.py-20 {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.py-40 {
  padding-top: 40px !important;
  padding-bottom: 40px !important;
}

.mt-15 {
  margin-top: 15px;
}

.mt-14 {
  margin-top: 14px !important;
}

.mb-14 {
  margin-bottom: 14px !important;
}

.mt-12 {
  margin-top: 12px !important;
}

.pt-13 {
  padding-top: 13px;
}

.gap-8 {
  gap: 8px !important;
}

.gp-4 {
  gap: 4px !important;
}

.gp-20 {
  gap: 20px !important;
}

.gp-8 {
  gap: 8px !important;
}

.pt-8 {
  padding-top: 8px;
}

.pb-20 {
  padding-bottom: 20px;
}

.px-20 {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.p-20 {
  padding: 20px !important;
}

.p-32 {
  padding: 32px !important;
}

.py-10 {
  padding-top: 10px;
  padding-bottom: 10px;
}

.px-16 {
  padding-left: 16px !important;
  padding-right: 16px !important;
}

.pe-15 {
  padding-right: 15px;
}

.ps-20 {
  padding-left: 20px;
}

.px-56 {
  padding-left: 56px;
  padding-right: 56px;
}

.pt-20 {
  padding-top: 20px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.pb-20 {
  padding-bottom: 20px !important;
}

.mt-40 {
  margin-top: 40px !important;
}

.mt-30 {
  margin-top: 30px !important;
}

.mt-8 {
  margin-top: 8px !important;
}

.w-36 {
  width: 36px !important;
}

.drawer-icon {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 104px;
  cursor: pointer;
  text-align: center;
  z-index: 1;
}

.guest-icon {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 104px;
  cursor: pointer;
  text-align: center;
  z-index: 1;
}

.home-icon {
  position: absolute;
  left: 0;
  top: 47%;
  transform: translateY(-50%);
  width: 104px;
  cursor: pointer;
  text-align: center;
}

.customer-menu-icon {
  margin-top: auto;
  margin-bottom: auto;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 104px;
}

.icon-row {
  margin: auto;
}

.icon-row:last-child {
  justify-content: right;
}

.custom-sign-in {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  min-height: 100vh;
  background-color: #dbe3e8;
}

.signin-card {
  background-color: #fff;
  padding: 21px 34px;
  border-radius: 36px;
}

.auto-width {
  flex: 0 0 auto;
  /* This makes the div take its content width */
}

.remaining-space {
  flex: 1;
  /* This makes the div take up all remaining space */
}

.category-wrapper {
  border-radius: 6px;
  box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.2);
  height: calc(100vh - 60px);
}

.with-divider::after {
  content: '';
  border-right: 1px solid #eaeaea;
  height: calc(100% - 200px);
  position: absolute;
  top: 15%;
  right: 34%;
  padding-left: 10px !important;
}

.font-poppins {
  font-family: Poppins !important;
}

.spinner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.5);
  z-index: 1000;
}

.item-line-padding {
  padding: 8px 10px;
}

.quantity-counter {
  position: absolute;

  top: -5px;
  right: -5px;
  background-color: #00c6b7;
  color: white;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
}

.square-button:active {
  background-color: #00c6b7;
  color: #fff;
}

.square-button:active .quantity-counter {
  display: none;
}

.sale-pos-addon-active {
  background-color: #00c6b7 !important;
  color: #fff !important;
}

.py-16 {
  padding-top: 16px;
  padding-bottom: 16px;
}

.assigned-variant-height {
  height: calc(100vh - 118px);
}

.pos-assigned-variant-height {
  height: calc(100vh - 306px);
}

.rearrange-wrapper {
  height: calc(100vh - 60px);
}

.rearrange-arrow {
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 4px;
  padding: 4px;
}

.rearrange-arrow svg {
  stroke: #303030;
}

.rearrange-arrow:focus {
  border: 1px solid #00c6b7;
  border-radius: 4px;
  outline: none;
  color: #858585;
}

.rearrange-arrow:active {
  border: 1px solid transparent;
}

.rearrange-arrow:active svg {
  stroke: #297189 !important;
}

.item-enter {
  opacity: 0;
}

.item-enter-active {
  opacity: 1;
  transition: opacity 300ms ease-in;
}

.item-exit {
  opacity: 1;
}

.item-exit-active {
  opacity: 0;
  transition: opacity 300ms ease-in;
}

.motion-container {
  position: relative;
  cursor: pointer;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #def0f6 !important;
  z-index: 1;
  pointer-events: none;
}

.line-item-box {
  overflow: hidden;
  position: relative;
}

.button-container {
  position: relative;
  z-index: 3;
}

.item-line-body {
  position: relative;
  z-index: 2;
  background-color: transparent !important;
}

.sale-pos-overlay-component {
  position: absolute;
  padding-top: 18px;
  z-index: 1;
  inset: 0;
  background-color: white;
  border-radius: 8px;
  transition:
    background-color 0.2s linear,
    transform 0.2s linear;
}

.border-manage-permission {
  border-top: 1px solid #eaeaea !important;
  border-left: 2px solid #eaeaea !important;
  border-right: 2px solid #eaeaea !important;
  border-bottom: 2px solid #eaeaea !important;
}

.border-manage-permission:first-child {
  border-bottom: 1px solid #eaeaea !important;
  border-top: 2px solid #eaeaea !important;
}

.multi-collapse {
  margin-left: 30px;
  margin-right: 30px;
  border-top: 2px solid #eaeaea !important;
}

.permission-checkbox:checked {
  background-color: #00c6b7 !important;
  border-color: #00c6b7 !important;
}

.permission-checkbox:disabled {
  background-color: #eaeaea !important;
  border-color: #eaeaea !important;
}

.permission-checkbox:focus {
  box-shadow: none !important;
  border-color: #00c6b7 !important;
}

.permission-checkbox {
  border: 1.5px solid #858585 !important;
  border-radius: 2px;
  padding: 10px;
  margin-right: 10px;
  position: relative;
}

.permission-checkbox::after {
  color: #a6a6a6 !important;
}

.form-check-input:checked[type='checkbox']:disabled {
  background: url('../../public/images/disabled-check-square.svg') !important;
  background-color: #eaeaea !important;
  background-position: center !important;
  /* Center the image */
  background-repeat: no-repeat !important;
  /* Prevent the image from repeating */
  background-size: 70% !important;
}

.toggle-modal-height {
  height: calc(100vh - 233px) !important;
  overflow-y: auto;
}

.manage-position {
  box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.2);
  padding: 20px;
  margin-top: 10px !important;
  border-radius: 4px;
  position: relative !important;
}

.manage-position .dropdown .dropdown-menu.show {
  box-shadow:
    0 4px 8px 0 rgba(255, 255, 255, 0.15),
    0 3px 9px 0 rgba(0, 0, 0, 0.19) !important;
}

.my-team-dropdown .dropdown-item-container:hover {
  background-color: #f8f9fa;
  cursor: pointer;
}

.manage-position ul {
  position: absolute !important;
  inset: 0px 0px auto auto !important;
  margin: 0px !important;
  transform: translate(0px, -16px) !important;
}

.manage-position .dropdown-web {
  border-radius: 4px !important;
}

.h-77 {
  height: 77px !important;
}

.font-weight-500 {
  font-weight: 500 !important;
}

.owner-icon {
  margin-right: 8px;
  margin-bottom: 4px;
}

.select-store-container {
  background-color: var(--select-store-bg-color);
  height: 100vh;
}

@media (max-height: 767px) {
  .select-store-container {
    background-color: var(--select-store-bg-color);
    height: auto !important;
  }
}

.select-store-card-contain {
  height: 380px;
  overflow-y: auto;
  padding: 0 40px 0 0;
}

.no-terminal-text-height {
  height: 360px;
}

.cursor-default {
  cursor: default !important;
}

.user-pin-key-pad-content-box {
  padding-left: 20px !important;
  padding-top: 40px !important;
}

.user-pin-modal-box {
  margin: 20px auto 0 auto;
  width: 75%;
}

.user-pin-modal-reopen-ticket {
  margin: 20px auto 0 auto;
  width: 75%;
}

.select-user-pin-modal {
  margin-top: 20px !important;
  box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  overflow-y: auto;
}

.main-logo-container {
  position: absolute;
  top: 30px;
  left: 30px;
  width: auto !important;
}

#tokenform {
  width: 100%;
}

#cccardlabel {
  color: #303030;
  font-family: 'poppins';
  font-weight: 500;
  line-height: 125%;
  font-size: 16px;
}

#ccexpirylabel {
  color: #303030;
  font-family: 'poppins';
  font-weight: 500;
  line-height: 125%;
  font-size: 16px;
}

#cccvvlabel {
  color: #303030;
  font-family: 'poppins';
  font-weight: 500;
  line-height: 125%;
  font-size: 16px;
}

#ccnumfield {
  width: 100%;
}

#ccexpirymonth {
  width: 50%;
}

#ccexpiryyear {
  width: 50%;
}

#cccvvfield {
  width: 100%;
}

#ccnumfield {
  border-radius: 6px;
  border: 2px solid #eaeaea;
  padding: 14px;
  color: #303030;
  outline-color: #00c6b7;
  font-family: 'Noto Sans', sans-serif;
  font-size: 14px;
  line-height: 125%;
  font-weight: 500;
  box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.2) !important;
  background-color: transparent;
}

#ccexpirymonth {
  border-radius: 6px;
  border: 2px solid #eaeaea;
  padding: 14px;
  color: #303030;
  outline-color: #00c6b7;
  font-family: 'Noto Sans', sans-serif;
  font-size: 14px;
  line-height: 125%;
  font-weight: 500;
  box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.2) !important;
  background-color: transparent;
}

#ccexpiryyear {
  border-radius: 6px;
  border: 2px solid #eaeaea;
  padding: 14px;
  color: #303030;
  outline-color: #00c6b7;
  font-family: 'Noto Sans', sans-serif;
  font-size: 14px;
  line-height: 125%;
  font-weight: 500;
  box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.2) !important;
  background-color: transparent;
}

#cccvvfield {
  border-radius: 6px;
  border: 2px solid #eaeaea;
  padding: 14px;
  color: #303030;
  outline-color: #00c6b7;
  font-family: 'Noto Sans', sans-serif;
  font-size: 14px;
  line-height: 125%;
  font-weight: 500;
  box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.2) !important;
  background-color: transparent;
}

#ccnumfield:disabled {
  background-color: #f1f1f1;
  color: #858585;
}

#ccexpirymonth:disabled {
  background-color: #f1f1f1;
  color: #858585;
}

#ccexpiryyear:disabled {
  background-color: #f1f1f1;
  color: #858585;
}

#cccvvfield:disabled {
  background-color: #f1f1f1;
  color: #858585;
}

#ccnumfield:focus {
  border: 2px solid #00c6b7;
}

#ccexpirymonth:focus {
  border: 2px solid #00c6b7;
}

#ccexpiryyear:focus {
  border: 2px solid #00c6b7;
}

#cccvvfield:focus {
  border: 2px solid #00c6b7;
}

#tokenform {
  width: 100%;
}

.max-width-0 {
  max-width: unset !important;
}

.select-user-modal-overlay {
  background: rgba(0, 0, 0, 0.2);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 1;
}

.edit-item-input-field input:disabled {
  background-color: transparent !important;
  color: #303030 !important;
}

.width-512 {
  width: 512px !important;
}

.cursor-default {
  cursor: default !important;
}

.selected-item-button {
  background-color: #00c6b7 !important;
  color: #ffffff !important;
}

.gap-8 {
  gap: 8px !important;
}

.edit-weighted-item-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.preset-variant:disabled {
  color: #303030 !important;
  border: none !important;
  box-shadow: none !important;
}

.webkit-fill-available {
  width: -webkit-fill-available;
}

.pos-tab-btn {
  border: none !important;
  background-color: var(--white-smoke) !important;
  outline: none !important;
  color: var(--text-element-subdued-dark) !important;
}

.pos-tab-btn.active {
  background-color: var(--white) !important;
  color: var(--primary-color) !important;
}

.pos-tab-container {
  display: flex;
  width: fit-content !important;
  justify-content: space-between;
  border-bottom: 2px solid var(--bright-gray);
}

.pos-tab-container-br :first-child {
  border-radius: 10px 0px 0px 0px;
}

.pos-tab-container-br :last-child {
  border-radius: 0px 10px 0px 0px;
}

.pos-tab-container .active {
  color: var(--primary-color) !important;
  border-bottom: 3px solid var(--brand-secondary) !important;
  overflow: hidden !important;
}

.quick-checkout-header-raw {
  background-color: var(--bright-gray);
  height: 40px;
  border: 1px solid var(--bright-gray);
}

.quick-checkout-header-raw th:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.quick-checkout-header-raw th:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.home-icon {
  position: absolute;
  left: 0;
  top: 47%;
  transform: translateY(-50%);
  width: 104px;
  cursor: pointer;
  text-align: center;
  flex-direction: column !important;
  background-color: transparent !important;
  border: none !important;
}

.home-icon-topbar {
  width: 104px;
  text-align: center;
  align-items: center;
  flex-direction: column !important;
  background-color: transparent !important;
  border: none !important;
}

.home-icon-topbar img {
  filter: brightness(0) invert(1);
}

.scrollbar-stable {
  scrollbar-gutter: stable both-edges !important;
}

.input-focus-color:focus {
  border-color: #eaeaea !important;
}

.remove-caret {
  caret-color: transparent !important;
}

.flag-class {
  position: absolute;
  top: 50%;
  bottom: 50%;
  transform: rotate(-37.388deg);
}

.ticket-flag-class {
  position: absolute;
  top: 50%;
  bottom: 50%;
  right: 16%;
  transform: rotate(-37.388deg);
}

.ticket-unclaimed-status-class {
  position: absolute;
  top: 50%;
  bottom: 50%;
  right: 7%;
  transform: rotate(-37.388deg);
}

.ticket-void-status-class {
  position: absolute;
  top: 47%;
  bottom: 50%;
  right: 13%;
  transform: rotate(-37.388deg);
}

@media screen and (min-width: 768px) and (max-width: 1099px) {
  .ticket-unclaimed-status-class {
    position: absolute;
    top: 43%;
    bottom: 48%;
    right: 2%;
    transform: rotate(-37.388deg);
  }
}

@media screen and (min-width: 1100px) and (max-width: 1299px) {
  .ticket-unclaimed-status-class {
    position: absolute;
    top: 43%;
    bottom: 48%;
    right: 6%;
    transform: rotate(-37.388deg);
  }
}

.flag-right {
  right: 14%;
}

.flag-left {
  left: 10%;
}

.fulfillment-modal .react-responsive-modal-closeButton {
  right: 36px !important;
}

.height-77 {
  height: 77% !important;
}

.img-white {
  filter: brightness(0) invert(1);
}

.payment-option-modal .react-responsive-modal-closeButton {
  margin-top: 3px;
}

.justify-end-gap14 {
  display: flex !important;
  justify-content: end;
  gap: 14px !important;
  align-items: center;
}

.d-justify-between {
  display: flex !important;
  justify-content: space-between;
  align-items: center;
}

.flex-column-center-start-gap4 {
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  gap: 4px !important;
}

.br-10 {
  border-radius: 10px;
}

.text-mode-nowrap {
  text-wrap-mode: nowrap;
}

.pos-display-icons {
  height: 50px;
  width: 50px;
}
