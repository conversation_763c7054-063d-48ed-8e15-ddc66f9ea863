{"compilerOptions": {"allowJs": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "declaration": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "moduleResolution": "node", "noEmit": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "pretty": true, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "es2018", "paths": {"@/*": ["./src/*"]}, "incremental": true, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "additional.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}