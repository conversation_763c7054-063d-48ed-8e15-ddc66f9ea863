{"name": "wash-and-buzz-web-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "check-types": "tsc --pretty --noEmit", "check-format": "prettier --check .", "check-lint": "eslint . --ext ts --ext tsx --ext js", "check-spell": "cspell . --no-progress", "format": "prettier --write .", "check-all": "npm run check-format && npm run check-lint && npm run check-spell && npm run check-types", "test": "jest", "test:watch": "jest --watch", "prepare": "cd ../.. && husky install ./frontend/wash-and-buzz-web-client/.husky", "test:e2e": "playwright test", "test:e2e-ui": "playwright test --ui", "test:e2e-headed": "playwright test --headed", "test:e2e-debug": "playwright test --debug", "test:e2e-codegen": "npx playwright codegen", "test:e2e-report": "playwright show-report", "test:e2e-report-allure": "allure open allure-report", "test:e2e-report-allure-generator": "allure generate allure-results --clean"}, "dependencies": {"@emotion/react": "^11.11.3", "@redux-devtools/extension": "^3.3.0", "@tanstack/react-table": "^8.17.3", "@types/lodash.debounce": "^4.0.9", "@types/react-slick": "^0.23.13", "axios": "^1.6.7", "big.js": "^6.2.1", "bootstrap": "^5.3.3", "cookie": "^1.0.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "dexie": "^3.2.5", "dotenv": "^16.4.5", "formik": "^2.4.5", "framer-motion": "^11.2.14", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash.debounce": "^4.0.8", "mqtt": "^5.9.0", "next": "^14.2.3", "next-auth": "^4.24.7", "next-redux-wrapper": "^8.1.0", "react": "^18", "react-animate-height": "^3.2.3", "react-bootstrap": "^2.10.1", "react-date-range": "^2.0.1", "react-dom": "^18", "react-icons": "^5.0.1", "react-idle-timer": "^5.7.2", "react-imask": "^7.6.1", "react-infinite-scroll-component": "^6.1.0", "react-input-mask": "^2.0.4", "react-phone-input-2": "^2.15.1", "react-redux": "^9.1.0", "react-responsive": "^10.0.0", "react-responsive-modal": "^6.4.2", "react-scroll": "^1.9.0", "react-select": "^5.8.0", "react-slick": "^0.30.2", "react-toastify": "^10.0.5", "recharts": "^2.12.7", "redux": "^5.0.1", "redux-saga": "^1.3.0", "slick-carousel": "^1.8.1", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "states-us": "^1.0.4", "theme-ui": "^0.16.2", "tmp": "^0.2.3", "uuid": "^9.0.1", "yup": "^1.3.3"}, "devDependencies": {"@commitlint/cli": "^19.2.2", "@commitlint/config-conventional": "^19.2.2", "@eslint/js": "^9.0.0", "@playwright/test": "^1.49.0", "@reduxjs/toolkit": "^2.2.5", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^15.0.2", "@types/axios": "^0.14.0", "@types/big.js": "^6.2.2", "@types/jest": "^29.5.12", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18.2.58", "@types/react-date-range": "^1.4.9", "@types/react-dom": "^18.2.19", "@types/react-input-mask": "^3.0.5", "@types/react-redux": "^7.1.33", "@types/react-scroll": "^1.8.10", "@types/redux": "^3.6.0", "@types/redux-devtools-extension": "^2.13.2", "@types/redux-saga": "^0.10.5", "@types/tmp": "^0.2.6", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.6.0", "@typescript-eslint/parser": "^7.6.0", "allure-commandline": "^2.32.0", "allure-playwright": "^3.0.6", "axios-mock-adapter": "^2.0.0", "cspell": "^8.8.0", "eslint": "^8.57.0", "eslint-config-eslint": "^9.0.0", "eslint-config-google": "^0.14.0", "eslint-config-next": "^14.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-next": "^0.0.0", "eslint-plugin-react": "^7.34.1", "globals": "^15.0.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.2", "prettier": "3.2.5", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5", "typescript-eslint": "^7.6.0"}, "engines": {"node": "20.x"}}